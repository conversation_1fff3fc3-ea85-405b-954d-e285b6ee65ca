'use server';

import { openai } from '@ai-sdk/openai';
import { generateText } from 'ai';
import { createAgent } from '@repo/data-access';

export const initAgent = async ({description}: {description: string}) => {
  const agentName = await generateText({
    model: openai('gpt-4o'),
    system: `You are a name generator for agents. Return only the name with no other content or fluff.`,
    messages: [
      {role: 'user', content: `Generate a name for an agent with the following description: ${description}`},
    ],
  });

  const agent = await createAgent({
    description,
    ownerId: 'c0202613-d5b0-4038-873d-9df9fac12de8', // tristan's userId
    povSummary: null,
    name: agentName.text,
  });

  return agent;
}