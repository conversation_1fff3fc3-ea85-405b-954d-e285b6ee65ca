'use server';

import { createSession, createSessionMessage, createSessionParticipant, updateAgent } from '@repo/data-access';

export const initAgentInterview = async ({agentId, povSummary}: {agentId: string; povSummary: string}) => {
  const agent = await updateAgent(agentId, {povSummary});
  const agentChatSession = await createSession({
    agentId,
    status: 'ACTIVE',
    type: 'INTERVIEW',
  });

  await createSessionParticipant({
    sessionId: agentChatSession.id,
    userId: agent.ownerId,
    role: 'OWNER',
  });

  await createSessionMessage({
    sessionId: agentChatSession.id,
    senderId: agent.ownerId,
    role: 'USER',
    content: povSummary,
  });

  return {
    session: agentChatSession,
    agent,
  };
}