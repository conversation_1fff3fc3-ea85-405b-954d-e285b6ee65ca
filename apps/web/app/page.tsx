'use client';

import { useState } from 'react';
import {useRouter} from 'next/navigation';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardAction, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { appRoutes } from '@/lib/routes';
import { initAgent } from '@/lib/server/initAgent';

export default function Home() {
  const router = useRouter();
  const [description, setDescription] = useState('');
  return (
    <div className="container mx-auto my-12">
      <div className="flex w-full">
        <div className="w-1/4">prior chats will go here</div>
        <Card className="grow">
          <CardHeader>
            <CardTitle>Create an agent</CardTitle>
            <CardDescription>Never repeat yourself again</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea value={description} onChange={e => setDescription(e.target.value)} />
          </CardContent>
          <CardFooter>
            <CardAction>
              <Button 
                onClick={async () => {
                  const agent = await initAgent({description});
                  router.push(appRoutes.manageAgent({agentId: agent.id}));
                }}>
                Create
              </Button>
            </CardAction>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
