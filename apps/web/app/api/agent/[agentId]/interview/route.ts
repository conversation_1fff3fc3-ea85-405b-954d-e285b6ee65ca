import {z} from 'zod';
import dedent from 'dedent';
import { openai } from '@ai-sdk/openai';
import { streamText, UIMessage, tool } from 'ai';
import { createSessionMessage, getOneSession } from '@repo/data-access';

export const maxDuration = 30;

export async function POST(req: Request) {
  const { 
    messages,
    id,
  }: { 
    messages: UIMessage[];
    id: string;
  } = await req.json();

  const session = await getOneSession({
    id,
  }, {
    agent: true,
  });

  const result = streamText({
    model: openai('o4-mini'),
    system: dedent`
      You are an interviewing agent tasked with deeply understanding and stress-testing the user's POV on a topic.
      Your goal is to capture their reasoning thoroughly so you can represent it later with high fidelity.

      ### Your Behavior:
      - Act like a skeptical stakeholder in a real company.
      - Ask challenging, realistic questions from the perspective of different personas (Finance, Product, Marketing, CEO, etc.).
      - One question at a time. Keep questions short and natural, like a real colleague.
      - If the user's answer is vague, incomplete, or evasive, ask a follow-up question before moving on.
      - Do NOT assume you understand if the answer lacks detail.
      - If the user seems confused, rephrase or clarify your question.

      ### Important:
      - You have access to a function \`recordAnswer(question, answer, persona)\` to store a completed Q&A pair.
      - ONLY call \`recordAnswer\` when:
          - The answer is clear, complete, and reflects the user's true reasoning.
          - All clarifying questions have been asked.
      - Do not call \`recordAnswer\` prematurely. Use judgment to ensure quality.

      ### Tone:
      - Professional but conversational.
      - Probing but not hostile. You're trying to understand, not attack.
      - If the user says "done" or "stop", wrap up politely.

      ### Current Context:
      - The user's POV: {{pov_summary}}

      Begin the interview now.
    `.replace('{{pov_summary}}', session?.agent.povSummary ?? ''),
    messages,
    tools: {
      recordAnswer: tool({
        description: "Record a completed Q&A pair",
        parameters: z.object({
          question: z.string().describe("The question asked"),
          answer: z.string().describe("The answer provided"),
          persona: z.string().describe("The persona of the questioner"),
        }),
        execute: async ({question, answer, persona}: {question: string; answer: string; persona: string}) => {
          console.log('recordAnswer ->', question, answer, persona);
          return {};
        },
      }),
    },
    async onFinish({response}) {
      const {messages} = response;
      const mostRecentMessage = messages[messages.length - 1];
      if (!mostRecentMessage) {
        return;
      }
      console.log('mostRecentMessage ->', mostRecentMessage);
      await createSessionMessage({
        sessionId: id,
        role: 'AGENT',
        senderId: null,
        // @ts-expect-error todo figure out typing
        content: mostRecentMessage.content.map(part => part.text).join(''),
      })
    },
    // maxSteps: 5,
  });

  const mostRecentMessage = messages[messages.length - 1];
  const role = mostRecentMessage && (mostRecentMessage.role === 'user' ? 'USER' : 'AGENT');
  if (role === 'USER' && mostRecentMessage && mostRecentMessage.content !== '') {
    await createSessionMessage({
      sessionId: id,
      senderId: role === 'USER' ? session?.agent.ownerId ?? null : null,
      role,
      content: mostRecentMessage.content,
    })
  }

  return result.toDataStreamResponse();
}
