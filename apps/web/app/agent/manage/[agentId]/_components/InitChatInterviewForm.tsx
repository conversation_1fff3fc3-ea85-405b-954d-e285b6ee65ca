'use client';

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardAction, CardContent, CardFooter } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import { appRoutes } from "@/lib/routes";
import { initAgentInterview } from "@/lib/server/initAgentInterview";
import { useState } from "react";

export const InitChatInterviewForm = ({agentId}: {agentId: string;}) => {
  const [povSummary, setPovSummary] = useState('');
  const router = useRouter();
  return (
    <>
      <CardContent>
        <Textarea value={povSummary} onChange={e => setPovSummary(e.target.value)} />
      </CardContent>
      <CardFooter>
        <CardAction>
          <Button onClick={async () => {
            const {session} = await initAgentInterview({agentId, povSummary});
            router.push(appRoutes.agentSession({sessionId: session.id}));
          }}>
            Next
          </Button>
        </CardAction>
      </CardFooter>
    </>
  );
}