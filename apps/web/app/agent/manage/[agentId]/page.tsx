'use server';

import Agent<PERSON><PERSON> from "@/components/chat/AgentChat";
import { ShineBorder } from "@/components/magicui/shine-border";
import { Card, CardAction, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { getAgentById } from "@repo/data-access";
import { InitChatInterviewForm } from "./_components/InitChatInterviewForm";

const loadAgent = async (agentId: string) => {
  const agent = await getAgentById(agentId);
  return {
    agent,
  }
}

const ManageAgentPage = async ({params}: {params: {agentId: string}}) => {
  const {agent} = await loadAgent(params.agentId);
  if (!agent) {
    return <div>Agent not found</div>;
  }

  return (
    <div className="min-h-screen flex flex-col">
      {!agent.povSummary ? (
        <div className="container flex items-center justify-center h-full mx-auto my-auto">
          <Card className="relative w-[600px]">
            <ShineBorder shineColor={["#A07CFE", "#FE8FB5", "#FFBE7B"]} />
            <CardHeader>
              <CardTitle>
                {agent.name}
              </CardTitle>
              <CardDescription>Give your agent a POV. Why do you believe what you believe?</CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea />
            </CardContent>
            <CardFooter>
              <CardAction>
                <InitChatInterviewForm agentId={agent.id} />
              </CardAction>
            </CardFooter>
          </Card>
        </div>
      ) : (
        <AgentChat 
          id={agent.id} 
          api={`/api/agent/${agent.id}/interview`} 
          initialMessages={}
        />
      )}
    </div>
  );
}

export default ManageAgentPage;
