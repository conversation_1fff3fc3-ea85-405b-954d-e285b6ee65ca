'use server';

import Agent<PERSON><PERSON> from "@/components/chat/AgentChat";
import { getOneSession } from "@repo/data-access";

const loadAgentInterviewSession = async ({agentId}: {agentId: string}) => {
  const session = await getOneSession({
    agentId,
    type: 'INTERVIEW',
  }, {
    messages: true,
    agent: true,
  });

  return {
    session,
  }
}

const AgentInterviewPage = async ({params}: {params: {agentId: string}}) => {
  const {session} = await loadAgentInterviewSession({agentId: params.agentId});
  if (!session) {
    return <div>Session not found</div>;
  }
  return (
    <div className="min-h-screen flex flex-col">
      <AgentChat
        id={session.id} 
        api={`/api/agent/${params.agentId}/interview`} 
        initialMessages={
          session.messages.map(msg => ({
            id: msg.id,
            role: msg.role === 'USER' ? 'user' : 'assistant',
            parts: [{type: 'text', text: msg.content}],
            content: msg.content,
          }))
        }
        autoInit={session.messages.length === 1}
      />
    </div>
  )
}

export default AgentInterviewPage;
