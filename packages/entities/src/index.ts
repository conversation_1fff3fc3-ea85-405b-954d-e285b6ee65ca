import { z } from 'zod';
import { Prisma } from '../../database/generated/client';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// JSON
//------------------------------------------------------

export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;

export const transformJsonNull = (v?: NullableJsonInput) => {
  if (!v || v === 'DbNull') return Prisma.DbNull;
  if (v === 'JsonNull') return Prisma.JsonNull;
  return v;
};

export const JsonValueSchema: z.ZodType<Prisma.JsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.literal(null),
    z.record(z.lazy(() => JsonValueSchema.optional())),
    z.array(z.lazy(() => JsonValueSchema)),
  ])
);

export type JsonValueType = z.infer<typeof JsonValueSchema>;

export const NullableJsonValue = z
  .union([JsonValueSchema, z.literal('DbNull'), z.literal('JsonNull')])
  .nullable()
  .transform((v) => transformJsonNull(v));

export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;

export const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.object({ toJSON: z.function(z.tuple([]), z.any()) }),
    z.record(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
    z.array(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
  ])
);

export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;


/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['ReadUncommitted','ReadCommitted','RepeatableRead','Serializable']);

export const TempEmbeddingsTableScalarFieldEnumSchema = z.enum(['id','parentId','type','text','metadata','createdAt','updatedAt']);

export const KnowledgeNodeEmbeddingsScalarFieldEnumSchema = z.enum(['id','nodeId']);

export const FooScalarFieldEnumSchema = z.enum(['id','name','email']);

export const UserScalarFieldEnumSchema = z.enum(['userId']);

export const KnowledgeNodeScalarFieldEnumSchema = z.enum(['id','content','type','source','context','validated','createdAt','updatedAt','authorId']);

export const NodeRelationshipScalarFieldEnumSchema = z.enum(['id','fromId','toId','type','createdAt','updatedAt']);

export const ValidationScalarFieldEnumSchema = z.enum(['id','nodeId','expertId','notes','createdAt','updatedAt']);

export const TopicScalarFieldEnumSchema = z.enum(['id','name','createdAt','updatedAt']);

export const AgentScalarFieldEnumSchema = z.enum(['id','name','description','povSummary','ownerId','createdAt','updatedAt']);

export const QnAPairScalarFieldEnumSchema = z.enum(['id','agentId','question','answer','persona','createdAt']);

export const QnAPairEmbeddingScalarFieldEnumSchema = z.enum(['id','qnaPairId','model','createdAt']);

export const SessionScalarFieldEnumSchema = z.enum(['id','agentId','status','createdAt','updatedAt','type']);

export const SessionMessageScalarFieldEnumSchema = z.enum(['id','sessionId','senderId','role','content','createdAt']);

export const SessionParticipantScalarFieldEnumSchema = z.enum(['id','sessionId','userId','role','joinedAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const NullableJsonNullValueInputSchema = z.enum(['DbNull','JsonNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.DbNull : value);

export const QueryModeSchema = z.enum(['default','insensitive']);

export const JsonNullValueFilterSchema = z.enum(['DbNull','JsonNull','AnyNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.JsonNull : value === 'AnyNull' ? Prisma.AnyNull : value);

export const NullsOrderSchema = z.enum(['first','last']);

export const SourceTypeSchema = z.enum(['slack','notion']);

export type SourceTypeType = `${z.infer<typeof SourceTypeSchema>}`

export const NodeTypeSchema = z.enum(['fact','decision','definition','question','answer']);

export type NodeTypeType = `${z.infer<typeof NodeTypeSchema>}`

export const RelationshipTypeSchema = z.enum(['answers','contradicts','supports','updates','authored_by','relates_to_topic','validated_by']);

export type RelationshipTypeType = `${z.infer<typeof RelationshipTypeSchema>}`

export const PersonaTypeSchema = z.enum(['FINANCE','PRODUCT','MARKETING','CEO','SALES','ENGINEERING']);

export type PersonaTypeType = `${z.infer<typeof PersonaTypeSchema>}`

export const SessionStatusSchema = z.enum(['ACTIVE','CLOSED']);

export type SessionStatusType = `${z.infer<typeof SessionStatusSchema>}`

export const RoleTypeSchema = z.enum(['USER','AGENT']);

export type RoleTypeType = `${z.infer<typeof RoleTypeSchema>}`

export const SessionRoleSchema = z.enum(['OWNER','PARTICIPANT']);

export type SessionRoleType = `${z.infer<typeof SessionRoleSchema>}`

export const SessionTypeSchema = z.enum(['CHAT','INTERVIEW']);

export type SessionTypeType = `${z.infer<typeof SessionTypeSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// TEMP EMBEDDINGS TABLE SCHEMA
/////////////////////////////////////////

export const TempEmbeddingsTableSchema = z.object({
  id: z.string().cuid(),
  parentId: z.string().nullable(),
  type: z.string().nullable(),
  text: z.string().nullable(),
  metadata: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type TempEmbeddingsTable = z.infer<typeof TempEmbeddingsTableSchema>

/////////////////////////////////////////
// KNOWLEDGE NODE EMBEDDINGS SCHEMA
/////////////////////////////////////////

export const KnowledgeNodeEmbeddingsSchema = z.object({
  id: z.string().cuid(),
  nodeId: z.string(),
})

export type KnowledgeNodeEmbeddings = z.infer<typeof KnowledgeNodeEmbeddingsSchema>

/////////////////////////////////////////
// FOO SCHEMA
/////////////////////////////////////////

export const FooSchema = z.object({
  id: z.number().int(),
  name: z.string(),
  email: z.string(),
})

export type Foo = z.infer<typeof FooSchema>

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  userId: z.string(),
})

export type User = z.infer<typeof UserSchema>

/////////////////////////////////////////
// KNOWLEDGE NODE SCHEMA
/////////////////////////////////////////

export const KnowledgeNodeSchema = z.object({
  type: NodeTypeSchema,
  source: SourceTypeSchema,
  id: z.string().cuid(),
  content: z.string(),
  context: JsonValueSchema.nullable(),
  validated: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  authorId: z.string(),
})

export type KnowledgeNode = z.infer<typeof KnowledgeNodeSchema>

/////////////////////////////////////////
// NODE RELATIONSHIP SCHEMA
/////////////////////////////////////////

export const NodeRelationshipSchema = z.object({
  type: RelationshipTypeSchema,
  id: z.string().cuid(),
  fromId: z.string(),
  toId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type NodeRelationship = z.infer<typeof NodeRelationshipSchema>

/////////////////////////////////////////
// VALIDATION SCHEMA
/////////////////////////////////////////

export const ValidationSchema = z.object({
  id: z.string().cuid(),
  nodeId: z.string(),
  expertId: z.string(),
  notes: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Validation = z.infer<typeof ValidationSchema>

/////////////////////////////////////////
// TOPIC SCHEMA
/////////////////////////////////////////

export const TopicSchema = z.object({
  id: z.string().cuid(),
  name: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Topic = z.infer<typeof TopicSchema>

/////////////////////////////////////////
// AGENT SCHEMA
/////////////////////////////////////////

export const AgentSchema = z.object({
  id: z.string().cuid(),
  name: z.string(),
  description: z.string().nullable(),
  povSummary: z.string().nullable(),
  ownerId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Agent = z.infer<typeof AgentSchema>

/////////////////////////////////////////
// QN A PAIR SCHEMA
/////////////////////////////////////////

export const QnAPairSchema = z.object({
  persona: PersonaTypeSchema,
  id: z.string().cuid(),
  agentId: z.string(),
  question: z.string(),
  answer: z.string(),
  createdAt: z.coerce.date(),
})

export type QnAPair = z.infer<typeof QnAPairSchema>

/////////////////////////////////////////
// QN A PAIR EMBEDDING SCHEMA
/////////////////////////////////////////

export const QnAPairEmbeddingSchema = z.object({
  id: z.string().cuid(),
  qnaPairId: z.string(),
  model: z.string(),
  createdAt: z.coerce.date(),
})

export type QnAPairEmbedding = z.infer<typeof QnAPairEmbeddingSchema>

/////////////////////////////////////////
// SESSION SCHEMA
/////////////////////////////////////////

export const SessionSchema = z.object({
  status: SessionStatusSchema,
  type: SessionTypeSchema,
  id: z.string().cuid(),
  agentId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Session = z.infer<typeof SessionSchema>

/////////////////////////////////////////
// SESSION MESSAGE SCHEMA
/////////////////////////////////////////

export const SessionMessageSchema = z.object({
  role: RoleTypeSchema,
  id: z.string().cuid(),
  sessionId: z.string(),
  senderId: z.string().nullable(),
  content: z.string(),
  createdAt: z.coerce.date(),
})

export type SessionMessage = z.infer<typeof SessionMessageSchema>

/////////////////////////////////////////
// SESSION PARTICIPANT SCHEMA
/////////////////////////////////////////

export const SessionParticipantSchema = z.object({
  role: SessionRoleSchema,
  id: z.string().cuid(),
  sessionId: z.string(),
  userId: z.string(),
  joinedAt: z.coerce.date(),
})

export type SessionParticipant = z.infer<typeof SessionParticipantSchema>

/////////////////////////////////////////
// SELECT & INCLUDE
/////////////////////////////////////////

// TEMP EMBEDDINGS TABLE
//------------------------------------------------------

export const TempEmbeddingsTableSelectSchema: z.ZodType<Prisma.TempEmbeddingsTableSelect> = z.object({
  id: z.boolean().optional(),
  parentId: z.boolean().optional(),
  type: z.boolean().optional(),
  text: z.boolean().optional(),
  metadata: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
}).strict()

// KNOWLEDGE NODE EMBEDDINGS
//------------------------------------------------------

export const KnowledgeNodeEmbeddingsSelectSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsSelect> = z.object({
  id: z.boolean().optional(),
  nodeId: z.boolean().optional(),
}).strict()

// FOO
//------------------------------------------------------

export const FooSelectSchema: z.ZodType<Prisma.FooSelect> = z.object({
  id: z.boolean().optional(),
  name: z.boolean().optional(),
  email: z.boolean().optional(),
}).strict()

// USER
//------------------------------------------------------

export const UserIncludeSchema: z.ZodType<Prisma.UserInclude> = z.object({
  nodes: z.union([z.boolean(),z.lazy(() => KnowledgeNodeFindManyArgsSchema)]).optional(),
  validations: z.union([z.boolean(),z.lazy(() => ValidationFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => UserCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const UserArgsSchema: z.ZodType<Prisma.UserDefaultArgs> = z.object({
  select: z.lazy(() => UserSelectSchema).optional(),
  include: z.lazy(() => UserIncludeSchema).optional(),
}).strict();

export const UserCountOutputTypeArgsSchema: z.ZodType<Prisma.UserCountOutputTypeDefaultArgs> = z.object({
  select: z.lazy(() => UserCountOutputTypeSelectSchema).nullish(),
}).strict();

export const UserCountOutputTypeSelectSchema: z.ZodType<Prisma.UserCountOutputTypeSelect> = z.object({
  nodes: z.boolean().optional(),
  validations: z.boolean().optional(),
}).strict();

export const UserSelectSchema: z.ZodType<Prisma.UserSelect> = z.object({
  userId: z.boolean().optional(),
  nodes: z.union([z.boolean(),z.lazy(() => KnowledgeNodeFindManyArgsSchema)]).optional(),
  validations: z.union([z.boolean(),z.lazy(() => ValidationFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => UserCountOutputTypeArgsSchema)]).optional(),
}).strict()

// KNOWLEDGE NODE
//------------------------------------------------------

export const KnowledgeNodeIncludeSchema: z.ZodType<Prisma.KnowledgeNodeInclude> = z.object({
  author: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
  relationshipsFrom: z.union([z.boolean(),z.lazy(() => NodeRelationshipFindManyArgsSchema)]).optional(),
  relationshipsTo: z.union([z.boolean(),z.lazy(() => NodeRelationshipFindManyArgsSchema)]).optional(),
  validations: z.union([z.boolean(),z.lazy(() => ValidationFindManyArgsSchema)]).optional(),
  topics: z.union([z.boolean(),z.lazy(() => TopicFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => KnowledgeNodeCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const KnowledgeNodeArgsSchema: z.ZodType<Prisma.KnowledgeNodeDefaultArgs> = z.object({
  select: z.lazy(() => KnowledgeNodeSelectSchema).optional(),
  include: z.lazy(() => KnowledgeNodeIncludeSchema).optional(),
}).strict();

export const KnowledgeNodeCountOutputTypeArgsSchema: z.ZodType<Prisma.KnowledgeNodeCountOutputTypeDefaultArgs> = z.object({
  select: z.lazy(() => KnowledgeNodeCountOutputTypeSelectSchema).nullish(),
}).strict();

export const KnowledgeNodeCountOutputTypeSelectSchema: z.ZodType<Prisma.KnowledgeNodeCountOutputTypeSelect> = z.object({
  relationshipsFrom: z.boolean().optional(),
  relationshipsTo: z.boolean().optional(),
  validations: z.boolean().optional(),
  topics: z.boolean().optional(),
}).strict();

export const KnowledgeNodeSelectSchema: z.ZodType<Prisma.KnowledgeNodeSelect> = z.object({
  id: z.boolean().optional(),
  content: z.boolean().optional(),
  type: z.boolean().optional(),
  source: z.boolean().optional(),
  context: z.boolean().optional(),
  validated: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  authorId: z.boolean().optional(),
  author: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
  relationshipsFrom: z.union([z.boolean(),z.lazy(() => NodeRelationshipFindManyArgsSchema)]).optional(),
  relationshipsTo: z.union([z.boolean(),z.lazy(() => NodeRelationshipFindManyArgsSchema)]).optional(),
  validations: z.union([z.boolean(),z.lazy(() => ValidationFindManyArgsSchema)]).optional(),
  topics: z.union([z.boolean(),z.lazy(() => TopicFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => KnowledgeNodeCountOutputTypeArgsSchema)]).optional(),
}).strict()

// NODE RELATIONSHIP
//------------------------------------------------------

export const NodeRelationshipIncludeSchema: z.ZodType<Prisma.NodeRelationshipInclude> = z.object({
  from: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
  to: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
}).strict()

export const NodeRelationshipArgsSchema: z.ZodType<Prisma.NodeRelationshipDefaultArgs> = z.object({
  select: z.lazy(() => NodeRelationshipSelectSchema).optional(),
  include: z.lazy(() => NodeRelationshipIncludeSchema).optional(),
}).strict();

export const NodeRelationshipSelectSchema: z.ZodType<Prisma.NodeRelationshipSelect> = z.object({
  id: z.boolean().optional(),
  fromId: z.boolean().optional(),
  toId: z.boolean().optional(),
  type: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  from: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
  to: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
}).strict()

// VALIDATION
//------------------------------------------------------

export const ValidationIncludeSchema: z.ZodType<Prisma.ValidationInclude> = z.object({
  node: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
  expert: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const ValidationArgsSchema: z.ZodType<Prisma.ValidationDefaultArgs> = z.object({
  select: z.lazy(() => ValidationSelectSchema).optional(),
  include: z.lazy(() => ValidationIncludeSchema).optional(),
}).strict();

export const ValidationSelectSchema: z.ZodType<Prisma.ValidationSelect> = z.object({
  id: z.boolean().optional(),
  nodeId: z.boolean().optional(),
  expertId: z.boolean().optional(),
  notes: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  node: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
  expert: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

// TOPIC
//------------------------------------------------------

export const TopicIncludeSchema: z.ZodType<Prisma.TopicInclude> = z.object({
  nodes: z.union([z.boolean(),z.lazy(() => KnowledgeNodeFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => TopicCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const TopicArgsSchema: z.ZodType<Prisma.TopicDefaultArgs> = z.object({
  select: z.lazy(() => TopicSelectSchema).optional(),
  include: z.lazy(() => TopicIncludeSchema).optional(),
}).strict();

export const TopicCountOutputTypeArgsSchema: z.ZodType<Prisma.TopicCountOutputTypeDefaultArgs> = z.object({
  select: z.lazy(() => TopicCountOutputTypeSelectSchema).nullish(),
}).strict();

export const TopicCountOutputTypeSelectSchema: z.ZodType<Prisma.TopicCountOutputTypeSelect> = z.object({
  nodes: z.boolean().optional(),
}).strict();

export const TopicSelectSchema: z.ZodType<Prisma.TopicSelect> = z.object({
  id: z.boolean().optional(),
  name: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  nodes: z.union([z.boolean(),z.lazy(() => KnowledgeNodeFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => TopicCountOutputTypeArgsSchema)]).optional(),
}).strict()

// AGENT
//------------------------------------------------------

export const AgentIncludeSchema: z.ZodType<Prisma.AgentInclude> = z.object({
  qnaPairs: z.union([z.boolean(),z.lazy(() => QnAPairFindManyArgsSchema)]).optional(),
  sessions: z.union([z.boolean(),z.lazy(() => SessionFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => AgentCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const AgentArgsSchema: z.ZodType<Prisma.AgentDefaultArgs> = z.object({
  select: z.lazy(() => AgentSelectSchema).optional(),
  include: z.lazy(() => AgentIncludeSchema).optional(),
}).strict();

export const AgentCountOutputTypeArgsSchema: z.ZodType<Prisma.AgentCountOutputTypeDefaultArgs> = z.object({
  select: z.lazy(() => AgentCountOutputTypeSelectSchema).nullish(),
}).strict();

export const AgentCountOutputTypeSelectSchema: z.ZodType<Prisma.AgentCountOutputTypeSelect> = z.object({
  qnaPairs: z.boolean().optional(),
  sessions: z.boolean().optional(),
}).strict();

export const AgentSelectSchema: z.ZodType<Prisma.AgentSelect> = z.object({
  id: z.boolean().optional(),
  name: z.boolean().optional(),
  description: z.boolean().optional(),
  povSummary: z.boolean().optional(),
  ownerId: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  qnaPairs: z.union([z.boolean(),z.lazy(() => QnAPairFindManyArgsSchema)]).optional(),
  sessions: z.union([z.boolean(),z.lazy(() => SessionFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => AgentCountOutputTypeArgsSchema)]).optional(),
}).strict()

// QN A PAIR
//------------------------------------------------------

export const QnAPairIncludeSchema: z.ZodType<Prisma.QnAPairInclude> = z.object({
  agent: z.union([z.boolean(),z.lazy(() => AgentArgsSchema)]).optional(),
}).strict()

export const QnAPairArgsSchema: z.ZodType<Prisma.QnAPairDefaultArgs> = z.object({
  select: z.lazy(() => QnAPairSelectSchema).optional(),
  include: z.lazy(() => QnAPairIncludeSchema).optional(),
}).strict();

export const QnAPairSelectSchema: z.ZodType<Prisma.QnAPairSelect> = z.object({
  id: z.boolean().optional(),
  agentId: z.boolean().optional(),
  question: z.boolean().optional(),
  answer: z.boolean().optional(),
  persona: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  agent: z.union([z.boolean(),z.lazy(() => AgentArgsSchema)]).optional(),
}).strict()

// QN A PAIR EMBEDDING
//------------------------------------------------------

export const QnAPairEmbeddingSelectSchema: z.ZodType<Prisma.QnAPairEmbeddingSelect> = z.object({
  id: z.boolean().optional(),
  qnaPairId: z.boolean().optional(),
  model: z.boolean().optional(),
  createdAt: z.boolean().optional(),
}).strict()

// SESSION
//------------------------------------------------------

export const SessionIncludeSchema: z.ZodType<Prisma.SessionInclude> = z.object({
  agent: z.union([z.boolean(),z.lazy(() => AgentArgsSchema)]).optional(),
  messages: z.union([z.boolean(),z.lazy(() => SessionMessageFindManyArgsSchema)]).optional(),
  participants: z.union([z.boolean(),z.lazy(() => SessionParticipantFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => SessionCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const SessionArgsSchema: z.ZodType<Prisma.SessionDefaultArgs> = z.object({
  select: z.lazy(() => SessionSelectSchema).optional(),
  include: z.lazy(() => SessionIncludeSchema).optional(),
}).strict();

export const SessionCountOutputTypeArgsSchema: z.ZodType<Prisma.SessionCountOutputTypeDefaultArgs> = z.object({
  select: z.lazy(() => SessionCountOutputTypeSelectSchema).nullish(),
}).strict();

export const SessionCountOutputTypeSelectSchema: z.ZodType<Prisma.SessionCountOutputTypeSelect> = z.object({
  messages: z.boolean().optional(),
  participants: z.boolean().optional(),
}).strict();

export const SessionSelectSchema: z.ZodType<Prisma.SessionSelect> = z.object({
  id: z.boolean().optional(),
  agentId: z.boolean().optional(),
  status: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  type: z.boolean().optional(),
  agent: z.union([z.boolean(),z.lazy(() => AgentArgsSchema)]).optional(),
  messages: z.union([z.boolean(),z.lazy(() => SessionMessageFindManyArgsSchema)]).optional(),
  participants: z.union([z.boolean(),z.lazy(() => SessionParticipantFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => SessionCountOutputTypeArgsSchema)]).optional(),
}).strict()

// SESSION MESSAGE
//------------------------------------------------------

export const SessionMessageIncludeSchema: z.ZodType<Prisma.SessionMessageInclude> = z.object({
  session: z.union([z.boolean(),z.lazy(() => SessionArgsSchema)]).optional(),
}).strict()

export const SessionMessageArgsSchema: z.ZodType<Prisma.SessionMessageDefaultArgs> = z.object({
  select: z.lazy(() => SessionMessageSelectSchema).optional(),
  include: z.lazy(() => SessionMessageIncludeSchema).optional(),
}).strict();

export const SessionMessageSelectSchema: z.ZodType<Prisma.SessionMessageSelect> = z.object({
  id: z.boolean().optional(),
  sessionId: z.boolean().optional(),
  senderId: z.boolean().optional(),
  role: z.boolean().optional(),
  content: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  session: z.union([z.boolean(),z.lazy(() => SessionArgsSchema)]).optional(),
}).strict()

// SESSION PARTICIPANT
//------------------------------------------------------

export const SessionParticipantIncludeSchema: z.ZodType<Prisma.SessionParticipantInclude> = z.object({
  session: z.union([z.boolean(),z.lazy(() => SessionArgsSchema)]).optional(),
}).strict()

export const SessionParticipantArgsSchema: z.ZodType<Prisma.SessionParticipantDefaultArgs> = z.object({
  select: z.lazy(() => SessionParticipantSelectSchema).optional(),
  include: z.lazy(() => SessionParticipantIncludeSchema).optional(),
}).strict();

export const SessionParticipantSelectSchema: z.ZodType<Prisma.SessionParticipantSelect> = z.object({
  id: z.boolean().optional(),
  sessionId: z.boolean().optional(),
  userId: z.boolean().optional(),
  role: z.boolean().optional(),
  joinedAt: z.boolean().optional(),
  session: z.union([z.boolean(),z.lazy(() => SessionArgsSchema)]).optional(),
}).strict()


/////////////////////////////////////////
// INPUT TYPES
/////////////////////////////////////////

export const TempEmbeddingsTableWhereInputSchema: z.ZodType<Prisma.TempEmbeddingsTableWhereInput> = z.object({
  AND: z.union([ z.lazy(() => TempEmbeddingsTableWhereInputSchema),z.lazy(() => TempEmbeddingsTableWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TempEmbeddingsTableWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TempEmbeddingsTableWhereInputSchema),z.lazy(() => TempEmbeddingsTableWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  parentId: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  type: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  text: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  metadata: z.lazy(() => JsonNullableFilterSchema).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const TempEmbeddingsTableOrderByWithRelationInputSchema: z.ZodType<Prisma.TempEmbeddingsTableOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  text: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  metadata: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TempEmbeddingsTableWhereUniqueInputSchema: z.ZodType<Prisma.TempEmbeddingsTableWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => TempEmbeddingsTableWhereInputSchema),z.lazy(() => TempEmbeddingsTableWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TempEmbeddingsTableWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TempEmbeddingsTableWhereInputSchema),z.lazy(() => TempEmbeddingsTableWhereInputSchema).array() ]).optional(),
  parentId: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  type: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  text: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  metadata: z.lazy(() => JsonNullableFilterSchema).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict());

export const TempEmbeddingsTableOrderByWithAggregationInputSchema: z.ZodType<Prisma.TempEmbeddingsTableOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  text: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  metadata: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => TempEmbeddingsTableCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => TempEmbeddingsTableMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => TempEmbeddingsTableMinOrderByAggregateInputSchema).optional()
}).strict();

export const TempEmbeddingsTableScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.TempEmbeddingsTableScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema),z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema),z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  parentId: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  type: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  text: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  metadata: z.lazy(() => JsonNullableWithAggregatesFilterSchema).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const KnowledgeNodeEmbeddingsWhereInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsWhereInput> = z.object({
  AND: z.union([ z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema),z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema),z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
}).strict();

export const KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeEmbeddingsWhereUniqueInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsWhereUniqueInput> = z.union([
  z.object({
    id: z.string().cuid(),
    nodeId: z.string()
  }),
  z.object({
    id: z.string().cuid(),
  }),
  z.object({
    nodeId: z.string(),
  }),
])
.and(z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string().optional(),
  AND: z.union([ z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema),z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema),z.lazy(() => KnowledgeNodeEmbeddingsWhereInputSchema).array() ]).optional(),
}).strict());

export const KnowledgeNodeEmbeddingsOrderByWithAggregationInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => KnowledgeNodeEmbeddingsCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => KnowledgeNodeEmbeddingsMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => KnowledgeNodeEmbeddingsMinOrderByAggregateInputSchema).optional()
}).strict();

export const KnowledgeNodeEmbeddingsScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => KnowledgeNodeEmbeddingsScalarWhereWithAggregatesInputSchema),z.lazy(() => KnowledgeNodeEmbeddingsScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeEmbeddingsScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeEmbeddingsScalarWhereWithAggregatesInputSchema),z.lazy(() => KnowledgeNodeEmbeddingsScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
}).strict();

export const FooWhereInputSchema: z.ZodType<Prisma.FooWhereInput> = z.object({
  AND: z.union([ z.lazy(() => FooWhereInputSchema),z.lazy(() => FooWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => FooWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => FooWhereInputSchema),z.lazy(() => FooWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => IntFilterSchema),z.number() ]).optional(),
  name: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  email: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
}).strict();

export const FooOrderByWithRelationInputSchema: z.ZodType<Prisma.FooOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooWhereUniqueInputSchema: z.ZodType<Prisma.FooWhereUniqueInput> = z.union([
  z.object({
    id: z.number().int(),
    name: z.string(),
    email: z.string()
  }),
  z.object({
    id: z.number().int(),
    name: z.string(),
  }),
  z.object({
    id: z.number().int(),
    email: z.string(),
  }),
  z.object({
    id: z.number().int(),
  }),
  z.object({
    name: z.string(),
    email: z.string(),
  }),
  z.object({
    name: z.string(),
  }),
  z.object({
    email: z.string(),
  }),
])
.and(z.object({
  id: z.number().int().optional(),
  name: z.string().optional(),
  email: z.string().optional(),
  AND: z.union([ z.lazy(() => FooWhereInputSchema),z.lazy(() => FooWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => FooWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => FooWhereInputSchema),z.lazy(() => FooWhereInputSchema).array() ]).optional(),
}).strict());

export const FooOrderByWithAggregationInputSchema: z.ZodType<Prisma.FooOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => FooCountOrderByAggregateInputSchema).optional(),
  _avg: z.lazy(() => FooAvgOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => FooMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => FooMinOrderByAggregateInputSchema).optional(),
  _sum: z.lazy(() => FooSumOrderByAggregateInputSchema).optional()
}).strict();

export const FooScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.FooScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => FooScalarWhereWithAggregatesInputSchema),z.lazy(() => FooScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => FooScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => FooScalarWhereWithAggregatesInputSchema),z.lazy(() => FooScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => IntWithAggregatesFilterSchema),z.number() ]).optional(),
  name: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  email: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
}).strict();

export const UserWhereInputSchema: z.ZodType<Prisma.UserWhereInput> = z.object({
  AND: z.union([ z.lazy(() => UserWhereInputSchema),z.lazy(() => UserWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => UserWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => UserWhereInputSchema),z.lazy(() => UserWhereInputSchema).array() ]).optional(),
  userId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeListRelationFilterSchema).optional(),
  validations: z.lazy(() => ValidationListRelationFilterSchema).optional()
}).strict();

export const UserOrderByWithRelationInputSchema: z.ZodType<Prisma.UserOrderByWithRelationInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional(),
  nodes: z.lazy(() => KnowledgeNodeOrderByRelationAggregateInputSchema).optional(),
  validations: z.lazy(() => ValidationOrderByRelationAggregateInputSchema).optional()
}).strict();

export const UserWhereUniqueInputSchema: z.ZodType<Prisma.UserWhereUniqueInput> = z.object({
  userId: z.string()
})
.and(z.object({
  userId: z.string().optional(),
  AND: z.union([ z.lazy(() => UserWhereInputSchema),z.lazy(() => UserWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => UserWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => UserWhereInputSchema),z.lazy(() => UserWhereInputSchema).array() ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeListRelationFilterSchema).optional(),
  validations: z.lazy(() => ValidationListRelationFilterSchema).optional()
}).strict());

export const UserOrderByWithAggregationInputSchema: z.ZodType<Prisma.UserOrderByWithAggregationInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => UserCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => UserMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => UserMinOrderByAggregateInputSchema).optional()
}).strict();

export const UserScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.UserScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => UserScalarWhereWithAggregatesInputSchema),z.lazy(() => UserScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => UserScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => UserScalarWhereWithAggregatesInputSchema),z.lazy(() => UserScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  userId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
}).strict();

export const KnowledgeNodeWhereInputSchema: z.ZodType<Prisma.KnowledgeNodeWhereInput> = z.object({
  AND: z.union([ z.lazy(() => KnowledgeNodeWhereInputSchema),z.lazy(() => KnowledgeNodeWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeWhereInputSchema),z.lazy(() => KnowledgeNodeWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumNodeTypeFilterSchema),z.lazy(() => NodeTypeSchema) ]).optional(),
  source: z.union([ z.lazy(() => EnumSourceTypeFilterSchema),z.lazy(() => SourceTypeSchema) ]).optional(),
  context: z.lazy(() => JsonNullableFilterSchema).optional(),
  validated: z.union([ z.lazy(() => BoolFilterSchema),z.boolean() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  authorId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  author: z.union([ z.lazy(() => UserScalarRelationFilterSchema),z.lazy(() => UserWhereInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipListRelationFilterSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipListRelationFilterSchema).optional(),
  validations: z.lazy(() => ValidationListRelationFilterSchema).optional(),
  topics: z.lazy(() => TopicListRelationFilterSchema).optional()
}).strict();

export const KnowledgeNodeOrderByWithRelationInputSchema: z.ZodType<Prisma.KnowledgeNodeOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  context: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional(),
  author: z.lazy(() => UserOrderByWithRelationInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipOrderByRelationAggregateInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipOrderByRelationAggregateInputSchema).optional(),
  validations: z.lazy(() => ValidationOrderByRelationAggregateInputSchema).optional(),
  topics: z.lazy(() => TopicOrderByRelationAggregateInputSchema).optional()
}).strict();

export const KnowledgeNodeWhereUniqueInputSchema: z.ZodType<Prisma.KnowledgeNodeWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => KnowledgeNodeWhereInputSchema),z.lazy(() => KnowledgeNodeWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeWhereInputSchema),z.lazy(() => KnowledgeNodeWhereInputSchema).array() ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumNodeTypeFilterSchema),z.lazy(() => NodeTypeSchema) ]).optional(),
  source: z.union([ z.lazy(() => EnumSourceTypeFilterSchema),z.lazy(() => SourceTypeSchema) ]).optional(),
  context: z.lazy(() => JsonNullableFilterSchema).optional(),
  validated: z.union([ z.lazy(() => BoolFilterSchema),z.boolean() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  authorId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  author: z.union([ z.lazy(() => UserScalarRelationFilterSchema),z.lazy(() => UserWhereInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipListRelationFilterSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipListRelationFilterSchema).optional(),
  validations: z.lazy(() => ValidationListRelationFilterSchema).optional(),
  topics: z.lazy(() => TopicListRelationFilterSchema).optional()
}).strict());

export const KnowledgeNodeOrderByWithAggregationInputSchema: z.ZodType<Prisma.KnowledgeNodeOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  context: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => KnowledgeNodeCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => KnowledgeNodeMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => KnowledgeNodeMinOrderByAggregateInputSchema).optional()
}).strict();

export const KnowledgeNodeScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.KnowledgeNodeScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema),z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema),z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  content: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumNodeTypeWithAggregatesFilterSchema),z.lazy(() => NodeTypeSchema) ]).optional(),
  source: z.union([ z.lazy(() => EnumSourceTypeWithAggregatesFilterSchema),z.lazy(() => SourceTypeSchema) ]).optional(),
  context: z.lazy(() => JsonNullableWithAggregatesFilterSchema).optional(),
  validated: z.union([ z.lazy(() => BoolWithAggregatesFilterSchema),z.boolean() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  authorId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
}).strict();

export const NodeRelationshipWhereInputSchema: z.ZodType<Prisma.NodeRelationshipWhereInput> = z.object({
  AND: z.union([ z.lazy(() => NodeRelationshipWhereInputSchema),z.lazy(() => NodeRelationshipWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => NodeRelationshipWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => NodeRelationshipWhereInputSchema),z.lazy(() => NodeRelationshipWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  fromId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  toId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumRelationshipTypeFilterSchema),z.lazy(() => RelationshipTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  from: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
  to: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipOrderByWithRelationInputSchema: z.ZodType<Prisma.NodeRelationshipOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  from: z.lazy(() => KnowledgeNodeOrderByWithRelationInputSchema).optional(),
  to: z.lazy(() => KnowledgeNodeOrderByWithRelationInputSchema).optional()
}).strict();

export const NodeRelationshipWhereUniqueInputSchema: z.ZodType<Prisma.NodeRelationshipWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => NodeRelationshipWhereInputSchema),z.lazy(() => NodeRelationshipWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => NodeRelationshipWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => NodeRelationshipWhereInputSchema),z.lazy(() => NodeRelationshipWhereInputSchema).array() ]).optional(),
  fromId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  toId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumRelationshipTypeFilterSchema),z.lazy(() => RelationshipTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  from: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
  to: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
}).strict());

export const NodeRelationshipOrderByWithAggregationInputSchema: z.ZodType<Prisma.NodeRelationshipOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => NodeRelationshipCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => NodeRelationshipMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => NodeRelationshipMinOrderByAggregateInputSchema).optional()
}).strict();

export const NodeRelationshipScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.NodeRelationshipScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema),z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema),z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  fromId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  toId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumRelationshipTypeWithAggregatesFilterSchema),z.lazy(() => RelationshipTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const ValidationWhereInputSchema: z.ZodType<Prisma.ValidationWhereInput> = z.object({
  AND: z.union([ z.lazy(() => ValidationWhereInputSchema),z.lazy(() => ValidationWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => ValidationWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => ValidationWhereInputSchema),z.lazy(() => ValidationWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  expertId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  notes: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  node: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
  expert: z.union([ z.lazy(() => UserScalarRelationFilterSchema),z.lazy(() => UserWhereInputSchema) ]).optional(),
}).strict();

export const ValidationOrderByWithRelationInputSchema: z.ZodType<Prisma.ValidationOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  node: z.lazy(() => KnowledgeNodeOrderByWithRelationInputSchema).optional(),
  expert: z.lazy(() => UserOrderByWithRelationInputSchema).optional()
}).strict();

export const ValidationWhereUniqueInputSchema: z.ZodType<Prisma.ValidationWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => ValidationWhereInputSchema),z.lazy(() => ValidationWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => ValidationWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => ValidationWhereInputSchema),z.lazy(() => ValidationWhereInputSchema).array() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  expertId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  notes: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  node: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
  expert: z.union([ z.lazy(() => UserScalarRelationFilterSchema),z.lazy(() => UserWhereInputSchema) ]).optional(),
}).strict());

export const ValidationOrderByWithAggregationInputSchema: z.ZodType<Prisma.ValidationOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => ValidationCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => ValidationMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => ValidationMinOrderByAggregateInputSchema).optional()
}).strict();

export const ValidationScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.ValidationScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema),z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema),z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  expertId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  notes: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const TopicWhereInputSchema: z.ZodType<Prisma.TopicWhereInput> = z.object({
  AND: z.union([ z.lazy(() => TopicWhereInputSchema),z.lazy(() => TopicWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TopicWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TopicWhereInputSchema),z.lazy(() => TopicWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  name: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeListRelationFilterSchema).optional()
}).strict();

export const TopicOrderByWithRelationInputSchema: z.ZodType<Prisma.TopicOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  nodes: z.lazy(() => KnowledgeNodeOrderByRelationAggregateInputSchema).optional()
}).strict();

export const TopicWhereUniqueInputSchema: z.ZodType<Prisma.TopicWhereUniqueInput> = z.union([
  z.object({
    id: z.string().cuid(),
    name: z.string()
  }),
  z.object({
    id: z.string().cuid(),
  }),
  z.object({
    name: z.string(),
  }),
])
.and(z.object({
  id: z.string().cuid().optional(),
  name: z.string().optional(),
  AND: z.union([ z.lazy(() => TopicWhereInputSchema),z.lazy(() => TopicWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TopicWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TopicWhereInputSchema),z.lazy(() => TopicWhereInputSchema).array() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeListRelationFilterSchema).optional()
}).strict());

export const TopicOrderByWithAggregationInputSchema: z.ZodType<Prisma.TopicOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => TopicCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => TopicMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => TopicMinOrderByAggregateInputSchema).optional()
}).strict();

export const TopicScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.TopicScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => TopicScalarWhereWithAggregatesInputSchema),z.lazy(() => TopicScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => TopicScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TopicScalarWhereWithAggregatesInputSchema),z.lazy(() => TopicScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  name: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const AgentWhereInputSchema: z.ZodType<Prisma.AgentWhereInput> = z.object({
  AND: z.union([ z.lazy(() => AgentWhereInputSchema),z.lazy(() => AgentWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => AgentWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => AgentWhereInputSchema),z.lazy(() => AgentWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  name: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  description: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  povSummary: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  ownerId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  qnaPairs: z.lazy(() => QnAPairListRelationFilterSchema).optional(),
  sessions: z.lazy(() => SessionListRelationFilterSchema).optional()
}).strict();

export const AgentOrderByWithRelationInputSchema: z.ZodType<Prisma.AgentOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  description: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  povSummary: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  ownerId: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  qnaPairs: z.lazy(() => QnAPairOrderByRelationAggregateInputSchema).optional(),
  sessions: z.lazy(() => SessionOrderByRelationAggregateInputSchema).optional()
}).strict();

export const AgentWhereUniqueInputSchema: z.ZodType<Prisma.AgentWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => AgentWhereInputSchema),z.lazy(() => AgentWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => AgentWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => AgentWhereInputSchema),z.lazy(() => AgentWhereInputSchema).array() ]).optional(),
  name: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  description: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  povSummary: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  ownerId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  qnaPairs: z.lazy(() => QnAPairListRelationFilterSchema).optional(),
  sessions: z.lazy(() => SessionListRelationFilterSchema).optional()
}).strict());

export const AgentOrderByWithAggregationInputSchema: z.ZodType<Prisma.AgentOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  description: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  povSummary: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  ownerId: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => AgentCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => AgentMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => AgentMinOrderByAggregateInputSchema).optional()
}).strict();

export const AgentScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.AgentScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => AgentScalarWhereWithAggregatesInputSchema),z.lazy(() => AgentScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => AgentScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => AgentScalarWhereWithAggregatesInputSchema),z.lazy(() => AgentScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  name: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  description: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  povSummary: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  ownerId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const QnAPairWhereInputSchema: z.ZodType<Prisma.QnAPairWhereInput> = z.object({
  AND: z.union([ z.lazy(() => QnAPairWhereInputSchema),z.lazy(() => QnAPairWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => QnAPairWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => QnAPairWhereInputSchema),z.lazy(() => QnAPairWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  agentId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  question: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  answer: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  persona: z.union([ z.lazy(() => EnumPersonaTypeFilterSchema),z.lazy(() => PersonaTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  agent: z.union([ z.lazy(() => AgentScalarRelationFilterSchema),z.lazy(() => AgentWhereInputSchema) ]).optional(),
}).strict();

export const QnAPairOrderByWithRelationInputSchema: z.ZodType<Prisma.QnAPairOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  question: z.lazy(() => SortOrderSchema).optional(),
  answer: z.lazy(() => SortOrderSchema).optional(),
  persona: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  agent: z.lazy(() => AgentOrderByWithRelationInputSchema).optional()
}).strict();

export const QnAPairWhereUniqueInputSchema: z.ZodType<Prisma.QnAPairWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => QnAPairWhereInputSchema),z.lazy(() => QnAPairWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => QnAPairWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => QnAPairWhereInputSchema),z.lazy(() => QnAPairWhereInputSchema).array() ]).optional(),
  agentId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  question: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  answer: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  persona: z.union([ z.lazy(() => EnumPersonaTypeFilterSchema),z.lazy(() => PersonaTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  agent: z.union([ z.lazy(() => AgentScalarRelationFilterSchema),z.lazy(() => AgentWhereInputSchema) ]).optional(),
}).strict());

export const QnAPairOrderByWithAggregationInputSchema: z.ZodType<Prisma.QnAPairOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  question: z.lazy(() => SortOrderSchema).optional(),
  answer: z.lazy(() => SortOrderSchema).optional(),
  persona: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => QnAPairCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => QnAPairMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => QnAPairMinOrderByAggregateInputSchema).optional()
}).strict();

export const QnAPairScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.QnAPairScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => QnAPairScalarWhereWithAggregatesInputSchema),z.lazy(() => QnAPairScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => QnAPairScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => QnAPairScalarWhereWithAggregatesInputSchema),z.lazy(() => QnAPairScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  agentId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  question: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  answer: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  persona: z.union([ z.lazy(() => EnumPersonaTypeWithAggregatesFilterSchema),z.lazy(() => PersonaTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const QnAPairEmbeddingWhereInputSchema: z.ZodType<Prisma.QnAPairEmbeddingWhereInput> = z.object({
  AND: z.union([ z.lazy(() => QnAPairEmbeddingWhereInputSchema),z.lazy(() => QnAPairEmbeddingWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => QnAPairEmbeddingWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => QnAPairEmbeddingWhereInputSchema),z.lazy(() => QnAPairEmbeddingWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  qnaPairId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  model: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const QnAPairEmbeddingOrderByWithRelationInputSchema: z.ZodType<Prisma.QnAPairEmbeddingOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  qnaPairId: z.lazy(() => SortOrderSchema).optional(),
  model: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const QnAPairEmbeddingWhereUniqueInputSchema: z.ZodType<Prisma.QnAPairEmbeddingWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => QnAPairEmbeddingWhereInputSchema),z.lazy(() => QnAPairEmbeddingWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => QnAPairEmbeddingWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => QnAPairEmbeddingWhereInputSchema),z.lazy(() => QnAPairEmbeddingWhereInputSchema).array() ]).optional(),
  qnaPairId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  model: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict());

export const QnAPairEmbeddingOrderByWithAggregationInputSchema: z.ZodType<Prisma.QnAPairEmbeddingOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  qnaPairId: z.lazy(() => SortOrderSchema).optional(),
  model: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => QnAPairEmbeddingCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => QnAPairEmbeddingMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => QnAPairEmbeddingMinOrderByAggregateInputSchema).optional()
}).strict();

export const QnAPairEmbeddingScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.QnAPairEmbeddingScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => QnAPairEmbeddingScalarWhereWithAggregatesInputSchema),z.lazy(() => QnAPairEmbeddingScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => QnAPairEmbeddingScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => QnAPairEmbeddingScalarWhereWithAggregatesInputSchema),z.lazy(() => QnAPairEmbeddingScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  qnaPairId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  model: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const SessionWhereInputSchema: z.ZodType<Prisma.SessionWhereInput> = z.object({
  AND: z.union([ z.lazy(() => SessionWhereInputSchema),z.lazy(() => SessionWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionWhereInputSchema),z.lazy(() => SessionWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  agentId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  status: z.union([ z.lazy(() => EnumSessionStatusFilterSchema),z.lazy(() => SessionStatusSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  type: z.union([ z.lazy(() => EnumSessionTypeFilterSchema),z.lazy(() => SessionTypeSchema) ]).optional(),
  agent: z.union([ z.lazy(() => AgentScalarRelationFilterSchema),z.lazy(() => AgentWhereInputSchema) ]).optional(),
  messages: z.lazy(() => SessionMessageListRelationFilterSchema).optional(),
  participants: z.lazy(() => SessionParticipantListRelationFilterSchema).optional()
}).strict();

export const SessionOrderByWithRelationInputSchema: z.ZodType<Prisma.SessionOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  status: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  agent: z.lazy(() => AgentOrderByWithRelationInputSchema).optional(),
  messages: z.lazy(() => SessionMessageOrderByRelationAggregateInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantOrderByRelationAggregateInputSchema).optional()
}).strict();

export const SessionWhereUniqueInputSchema: z.ZodType<Prisma.SessionWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => SessionWhereInputSchema),z.lazy(() => SessionWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionWhereInputSchema),z.lazy(() => SessionWhereInputSchema).array() ]).optional(),
  agentId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  status: z.union([ z.lazy(() => EnumSessionStatusFilterSchema),z.lazy(() => SessionStatusSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  type: z.union([ z.lazy(() => EnumSessionTypeFilterSchema),z.lazy(() => SessionTypeSchema) ]).optional(),
  agent: z.union([ z.lazy(() => AgentScalarRelationFilterSchema),z.lazy(() => AgentWhereInputSchema) ]).optional(),
  messages: z.lazy(() => SessionMessageListRelationFilterSchema).optional(),
  participants: z.lazy(() => SessionParticipantListRelationFilterSchema).optional()
}).strict());

export const SessionOrderByWithAggregationInputSchema: z.ZodType<Prisma.SessionOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  status: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => SessionCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => SessionMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => SessionMinOrderByAggregateInputSchema).optional()
}).strict();

export const SessionScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.SessionScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => SessionScalarWhereWithAggregatesInputSchema),z.lazy(() => SessionScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionScalarWhereWithAggregatesInputSchema),z.lazy(() => SessionScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  agentId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  status: z.union([ z.lazy(() => EnumSessionStatusWithAggregatesFilterSchema),z.lazy(() => SessionStatusSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  type: z.union([ z.lazy(() => EnumSessionTypeWithAggregatesFilterSchema),z.lazy(() => SessionTypeSchema) ]).optional(),
}).strict();

export const SessionMessageWhereInputSchema: z.ZodType<Prisma.SessionMessageWhereInput> = z.object({
  AND: z.union([ z.lazy(() => SessionMessageWhereInputSchema),z.lazy(() => SessionMessageWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionMessageWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionMessageWhereInputSchema),z.lazy(() => SessionMessageWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  sessionId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  senderId: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  role: z.union([ z.lazy(() => EnumRoleTypeFilterSchema),z.lazy(() => RoleTypeSchema) ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  session: z.union([ z.lazy(() => SessionScalarRelationFilterSchema),z.lazy(() => SessionWhereInputSchema) ]).optional(),
}).strict();

export const SessionMessageOrderByWithRelationInputSchema: z.ZodType<Prisma.SessionMessageOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  senderId: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  session: z.lazy(() => SessionOrderByWithRelationInputSchema).optional()
}).strict();

export const SessionMessageWhereUniqueInputSchema: z.ZodType<Prisma.SessionMessageWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => SessionMessageWhereInputSchema),z.lazy(() => SessionMessageWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionMessageWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionMessageWhereInputSchema),z.lazy(() => SessionMessageWhereInputSchema).array() ]).optional(),
  sessionId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  senderId: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  role: z.union([ z.lazy(() => EnumRoleTypeFilterSchema),z.lazy(() => RoleTypeSchema) ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  session: z.union([ z.lazy(() => SessionScalarRelationFilterSchema),z.lazy(() => SessionWhereInputSchema) ]).optional(),
}).strict());

export const SessionMessageOrderByWithAggregationInputSchema: z.ZodType<Prisma.SessionMessageOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  senderId: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => SessionMessageCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => SessionMessageMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => SessionMessageMinOrderByAggregateInputSchema).optional()
}).strict();

export const SessionMessageScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.SessionMessageScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => SessionMessageScalarWhereWithAggregatesInputSchema),z.lazy(() => SessionMessageScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionMessageScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionMessageScalarWhereWithAggregatesInputSchema),z.lazy(() => SessionMessageScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  sessionId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  senderId: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  role: z.union([ z.lazy(() => EnumRoleTypeWithAggregatesFilterSchema),z.lazy(() => RoleTypeSchema) ]).optional(),
  content: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const SessionParticipantWhereInputSchema: z.ZodType<Prisma.SessionParticipantWhereInput> = z.object({
  AND: z.union([ z.lazy(() => SessionParticipantWhereInputSchema),z.lazy(() => SessionParticipantWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionParticipantWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionParticipantWhereInputSchema),z.lazy(() => SessionParticipantWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  sessionId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  userId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  role: z.union([ z.lazy(() => EnumSessionRoleFilterSchema),z.lazy(() => SessionRoleSchema) ]).optional(),
  joinedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  session: z.union([ z.lazy(() => SessionScalarRelationFilterSchema),z.lazy(() => SessionWhereInputSchema) ]).optional(),
}).strict();

export const SessionParticipantOrderByWithRelationInputSchema: z.ZodType<Prisma.SessionParticipantOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  userId: z.lazy(() => SortOrderSchema).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  joinedAt: z.lazy(() => SortOrderSchema).optional(),
  session: z.lazy(() => SessionOrderByWithRelationInputSchema).optional()
}).strict();

export const SessionParticipantWhereUniqueInputSchema: z.ZodType<Prisma.SessionParticipantWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => SessionParticipantWhereInputSchema),z.lazy(() => SessionParticipantWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionParticipantWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionParticipantWhereInputSchema),z.lazy(() => SessionParticipantWhereInputSchema).array() ]).optional(),
  sessionId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  userId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  role: z.union([ z.lazy(() => EnumSessionRoleFilterSchema),z.lazy(() => SessionRoleSchema) ]).optional(),
  joinedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  session: z.union([ z.lazy(() => SessionScalarRelationFilterSchema),z.lazy(() => SessionWhereInputSchema) ]).optional(),
}).strict());

export const SessionParticipantOrderByWithAggregationInputSchema: z.ZodType<Prisma.SessionParticipantOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  userId: z.lazy(() => SortOrderSchema).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  joinedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => SessionParticipantCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => SessionParticipantMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => SessionParticipantMinOrderByAggregateInputSchema).optional()
}).strict();

export const SessionParticipantScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.SessionParticipantScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => SessionParticipantScalarWhereWithAggregatesInputSchema),z.lazy(() => SessionParticipantScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionParticipantScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionParticipantScalarWhereWithAggregatesInputSchema),z.lazy(() => SessionParticipantScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  sessionId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  userId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  role: z.union([ z.lazy(() => EnumSessionRoleWithAggregatesFilterSchema),z.lazy(() => SessionRoleSchema) ]).optional(),
  joinedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const TempEmbeddingsTableUpdateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  parentId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  type: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  text: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  metadata: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TempEmbeddingsTableUncheckedUpdateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  parentId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  type: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  text: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  metadata: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TempEmbeddingsTableUpdateManyMutationInputSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  parentId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  type: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  text: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  metadata: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TempEmbeddingsTableUncheckedUpdateManyInputSchema: z.ZodType<Prisma.TempEmbeddingsTableUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  parentId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  type: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  text: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  metadata: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeEmbeddingsUpdateInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeEmbeddingsUncheckedUpdateInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeEmbeddingsUpdateManyMutationInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeEmbeddingsUncheckedUpdateManyInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const FooCreateInputSchema: z.ZodType<Prisma.FooCreateInput> = z.object({
  name: z.string(),
  email: z.string()
}).strict();

export const FooUncheckedCreateInputSchema: z.ZodType<Prisma.FooUncheckedCreateInput> = z.object({
  id: z.number().int().optional(),
  name: z.string(),
  email: z.string()
}).strict();

export const FooUpdateInputSchema: z.ZodType<Prisma.FooUpdateInput> = z.object({
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const FooUncheckedUpdateInputSchema: z.ZodType<Prisma.FooUncheckedUpdateInput> = z.object({
  id: z.union([ z.number().int(),z.lazy(() => IntFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const FooCreateManyInputSchema: z.ZodType<Prisma.FooCreateManyInput> = z.object({
  id: z.number().int().optional(),
  name: z.string(),
  email: z.string()
}).strict();

export const FooUpdateManyMutationInputSchema: z.ZodType<Prisma.FooUpdateManyMutationInput> = z.object({
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const FooUncheckedUpdateManyInputSchema: z.ZodType<Prisma.FooUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.number().int(),z.lazy(() => IntFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const UserCreateInputSchema: z.ZodType<Prisma.UserCreateInput> = z.object({
  userId: z.string(),
  nodes: z.lazy(() => KnowledgeNodeCreateNestedManyWithoutAuthorInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutExpertInputSchema).optional()
}).strict();

export const UserUncheckedCreateInputSchema: z.ZodType<Prisma.UserUncheckedCreateInput> = z.object({
  userId: z.string(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedCreateNestedManyWithoutAuthorInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutExpertInputSchema).optional()
}).strict();

export const UserUpdateInputSchema: z.ZodType<Prisma.UserUpdateInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUpdateManyWithoutAuthorNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutExpertNestedInputSchema).optional()
}).strict();

export const UserUncheckedUpdateInputSchema: z.ZodType<Prisma.UserUncheckedUpdateInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutAuthorNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutExpertNestedInputSchema).optional()
}).strict();

export const UserCreateManyInputSchema: z.ZodType<Prisma.UserCreateManyInput> = z.object({
  userId: z.string()
}).strict();

export const UserUpdateManyMutationInputSchema: z.ZodType<Prisma.UserUpdateManyMutationInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const UserUncheckedUpdateManyInputSchema: z.ZodType<Prisma.UserUncheckedUpdateManyInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeCreateInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateManyInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string()
}).strict();

export const KnowledgeNodeUpdateManyMutationInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedUpdateManyInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipCreateInputSchema: z.ZodType<Prisma.NodeRelationshipCreateInput> = z.object({
  id: z.string().cuid().optional(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  from: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutRelationshipsFromInputSchema),
  to: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutRelationshipsToInputSchema)
}).strict();

export const NodeRelationshipUncheckedCreateInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  fromId: z.string(),
  toId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipUpdateInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  from: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutRelationshipsFromNestedInputSchema).optional(),
  to: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutRelationshipsToNestedInputSchema).optional()
}).strict();

export const NodeRelationshipUncheckedUpdateInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  fromId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  toId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipCreateManyInputSchema: z.ZodType<Prisma.NodeRelationshipCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  fromId: z.string(),
  toId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipUpdateManyMutationInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  fromId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  toId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationCreateInputSchema: z.ZodType<Prisma.ValidationCreateInput> = z.object({
  id: z.string().cuid().optional(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  node: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutValidationsInputSchema),
  expert: z.lazy(() => UserCreateNestedOneWithoutValidationsInputSchema)
}).strict();

export const ValidationUncheckedCreateInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string(),
  expertId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationUpdateInputSchema: z.ZodType<Prisma.ValidationUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  node: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutValidationsNestedInputSchema).optional(),
  expert: z.lazy(() => UserUpdateOneRequiredWithoutValidationsNestedInputSchema).optional()
}).strict();

export const ValidationUncheckedUpdateInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  expertId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationCreateManyInputSchema: z.ZodType<Prisma.ValidationCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string(),
  expertId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationUpdateManyMutationInputSchema: z.ZodType<Prisma.ValidationUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  expertId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicCreateInputSchema: z.ZodType<Prisma.TopicCreateInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  nodes: z.lazy(() => KnowledgeNodeCreateNestedManyWithoutTopicsInputSchema).optional()
}).strict();

export const TopicUncheckedCreateInputSchema: z.ZodType<Prisma.TopicUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedCreateNestedManyWithoutTopicsInputSchema).optional()
}).strict();

export const TopicUpdateInputSchema: z.ZodType<Prisma.TopicUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUpdateManyWithoutTopicsNestedInputSchema).optional()
}).strict();

export const TopicUncheckedUpdateInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutTopicsNestedInputSchema).optional()
}).strict();

export const TopicCreateManyInputSchema: z.ZodType<Prisma.TopicCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const TopicUpdateManyMutationInputSchema: z.ZodType<Prisma.TopicUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicUncheckedUpdateManyInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const AgentCreateInputSchema: z.ZodType<Prisma.AgentCreateInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  description: z.string().optional().nullable(),
  povSummary: z.string().optional().nullable(),
  ownerId: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  qnaPairs: z.lazy(() => QnAPairCreateNestedManyWithoutAgentInputSchema).optional(),
  sessions: z.lazy(() => SessionCreateNestedManyWithoutAgentInputSchema).optional()
}).strict();

export const AgentUncheckedCreateInputSchema: z.ZodType<Prisma.AgentUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  description: z.string().optional().nullable(),
  povSummary: z.string().optional().nullable(),
  ownerId: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  qnaPairs: z.lazy(() => QnAPairUncheckedCreateNestedManyWithoutAgentInputSchema).optional(),
  sessions: z.lazy(() => SessionUncheckedCreateNestedManyWithoutAgentInputSchema).optional()
}).strict();

export const AgentUpdateInputSchema: z.ZodType<Prisma.AgentUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  description: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  povSummary: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  ownerId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  qnaPairs: z.lazy(() => QnAPairUpdateManyWithoutAgentNestedInputSchema).optional(),
  sessions: z.lazy(() => SessionUpdateManyWithoutAgentNestedInputSchema).optional()
}).strict();

export const AgentUncheckedUpdateInputSchema: z.ZodType<Prisma.AgentUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  description: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  povSummary: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  ownerId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  qnaPairs: z.lazy(() => QnAPairUncheckedUpdateManyWithoutAgentNestedInputSchema).optional(),
  sessions: z.lazy(() => SessionUncheckedUpdateManyWithoutAgentNestedInputSchema).optional()
}).strict();

export const AgentCreateManyInputSchema: z.ZodType<Prisma.AgentCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  description: z.string().optional().nullable(),
  povSummary: z.string().optional().nullable(),
  ownerId: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const AgentUpdateManyMutationInputSchema: z.ZodType<Prisma.AgentUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  description: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  povSummary: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  ownerId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const AgentUncheckedUpdateManyInputSchema: z.ZodType<Prisma.AgentUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  description: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  povSummary: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  ownerId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairCreateInputSchema: z.ZodType<Prisma.QnAPairCreateInput> = z.object({
  id: z.string().cuid().optional(),
  question: z.string(),
  answer: z.string(),
  persona: z.lazy(() => PersonaTypeSchema),
  createdAt: z.coerce.date().optional(),
  agent: z.lazy(() => AgentCreateNestedOneWithoutQnaPairsInputSchema)
}).strict();

export const QnAPairUncheckedCreateInputSchema: z.ZodType<Prisma.QnAPairUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  agentId: z.string(),
  question: z.string(),
  answer: z.string(),
  persona: z.lazy(() => PersonaTypeSchema),
  createdAt: z.coerce.date().optional()
}).strict();

export const QnAPairUpdateInputSchema: z.ZodType<Prisma.QnAPairUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  question: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  answer: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  persona: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => EnumPersonaTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  agent: z.lazy(() => AgentUpdateOneRequiredWithoutQnaPairsNestedInputSchema).optional()
}).strict();

export const QnAPairUncheckedUpdateInputSchema: z.ZodType<Prisma.QnAPairUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  agentId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  question: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  answer: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  persona: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => EnumPersonaTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairCreateManyInputSchema: z.ZodType<Prisma.QnAPairCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  agentId: z.string(),
  question: z.string(),
  answer: z.string(),
  persona: z.lazy(() => PersonaTypeSchema),
  createdAt: z.coerce.date().optional()
}).strict();

export const QnAPairUpdateManyMutationInputSchema: z.ZodType<Prisma.QnAPairUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  question: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  answer: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  persona: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => EnumPersonaTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairUncheckedUpdateManyInputSchema: z.ZodType<Prisma.QnAPairUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  agentId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  question: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  answer: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  persona: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => EnumPersonaTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairEmbeddingUpdateInputSchema: z.ZodType<Prisma.QnAPairEmbeddingUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  qnaPairId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  model: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairEmbeddingUncheckedUpdateInputSchema: z.ZodType<Prisma.QnAPairEmbeddingUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  qnaPairId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  model: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairEmbeddingUpdateManyMutationInputSchema: z.ZodType<Prisma.QnAPairEmbeddingUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  qnaPairId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  model: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairEmbeddingUncheckedUpdateManyInputSchema: z.ZodType<Prisma.QnAPairEmbeddingUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  qnaPairId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  model: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionCreateInputSchema: z.ZodType<Prisma.SessionCreateInput> = z.object({
  id: z.string().cuid().optional(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional(),
  agent: z.lazy(() => AgentCreateNestedOneWithoutSessionsInputSchema),
  messages: z.lazy(() => SessionMessageCreateNestedManyWithoutSessionInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantCreateNestedManyWithoutSessionInputSchema).optional()
}).strict();

export const SessionUncheckedCreateInputSchema: z.ZodType<Prisma.SessionUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  agentId: z.string(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional(),
  messages: z.lazy(() => SessionMessageUncheckedCreateNestedManyWithoutSessionInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantUncheckedCreateNestedManyWithoutSessionInputSchema).optional()
}).strict();

export const SessionUpdateInputSchema: z.ZodType<Prisma.SessionUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
  agent: z.lazy(() => AgentUpdateOneRequiredWithoutSessionsNestedInputSchema).optional(),
  messages: z.lazy(() => SessionMessageUpdateManyWithoutSessionNestedInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantUpdateManyWithoutSessionNestedInputSchema).optional()
}).strict();

export const SessionUncheckedUpdateInputSchema: z.ZodType<Prisma.SessionUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  agentId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
  messages: z.lazy(() => SessionMessageUncheckedUpdateManyWithoutSessionNestedInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantUncheckedUpdateManyWithoutSessionNestedInputSchema).optional()
}).strict();

export const SessionCreateManyInputSchema: z.ZodType<Prisma.SessionCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  agentId: z.string(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional()
}).strict();

export const SessionUpdateManyMutationInputSchema: z.ZodType<Prisma.SessionUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionUncheckedUpdateManyInputSchema: z.ZodType<Prisma.SessionUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  agentId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionMessageCreateInputSchema: z.ZodType<Prisma.SessionMessageCreateInput> = z.object({
  id: z.string().cuid().optional(),
  senderId: z.string().optional().nullable(),
  role: z.lazy(() => RoleTypeSchema),
  content: z.string(),
  createdAt: z.coerce.date().optional(),
  session: z.lazy(() => SessionCreateNestedOneWithoutMessagesInputSchema)
}).strict();

export const SessionMessageUncheckedCreateInputSchema: z.ZodType<Prisma.SessionMessageUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  sessionId: z.string(),
  senderId: z.string().optional().nullable(),
  role: z.lazy(() => RoleTypeSchema),
  content: z.string(),
  createdAt: z.coerce.date().optional()
}).strict();

export const SessionMessageUpdateInputSchema: z.ZodType<Prisma.SessionMessageUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  senderId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  role: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => EnumRoleTypeFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  session: z.lazy(() => SessionUpdateOneRequiredWithoutMessagesNestedInputSchema).optional()
}).strict();

export const SessionMessageUncheckedUpdateInputSchema: z.ZodType<Prisma.SessionMessageUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  sessionId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  senderId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  role: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => EnumRoleTypeFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionMessageCreateManyInputSchema: z.ZodType<Prisma.SessionMessageCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  sessionId: z.string(),
  senderId: z.string().optional().nullable(),
  role: z.lazy(() => RoleTypeSchema),
  content: z.string(),
  createdAt: z.coerce.date().optional()
}).strict();

export const SessionMessageUpdateManyMutationInputSchema: z.ZodType<Prisma.SessionMessageUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  senderId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  role: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => EnumRoleTypeFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionMessageUncheckedUpdateManyInputSchema: z.ZodType<Prisma.SessionMessageUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  sessionId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  senderId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  role: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => EnumRoleTypeFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionParticipantCreateInputSchema: z.ZodType<Prisma.SessionParticipantCreateInput> = z.object({
  id: z.string().cuid().optional(),
  userId: z.string(),
  role: z.lazy(() => SessionRoleSchema),
  joinedAt: z.coerce.date().optional(),
  session: z.lazy(() => SessionCreateNestedOneWithoutParticipantsInputSchema)
}).strict();

export const SessionParticipantUncheckedCreateInputSchema: z.ZodType<Prisma.SessionParticipantUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  sessionId: z.string(),
  userId: z.string(),
  role: z.lazy(() => SessionRoleSchema),
  joinedAt: z.coerce.date().optional()
}).strict();

export const SessionParticipantUpdateInputSchema: z.ZodType<Prisma.SessionParticipantUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => EnumSessionRoleFieldUpdateOperationsInputSchema) ]).optional(),
  joinedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  session: z.lazy(() => SessionUpdateOneRequiredWithoutParticipantsNestedInputSchema).optional()
}).strict();

export const SessionParticipantUncheckedUpdateInputSchema: z.ZodType<Prisma.SessionParticipantUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  sessionId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => EnumSessionRoleFieldUpdateOperationsInputSchema) ]).optional(),
  joinedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionParticipantCreateManyInputSchema: z.ZodType<Prisma.SessionParticipantCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  sessionId: z.string(),
  userId: z.string(),
  role: z.lazy(() => SessionRoleSchema),
  joinedAt: z.coerce.date().optional()
}).strict();

export const SessionParticipantUpdateManyMutationInputSchema: z.ZodType<Prisma.SessionParticipantUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => EnumSessionRoleFieldUpdateOperationsInputSchema) ]).optional(),
  joinedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionParticipantUncheckedUpdateManyInputSchema: z.ZodType<Prisma.SessionParticipantUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  sessionId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => EnumSessionRoleFieldUpdateOperationsInputSchema) ]).optional(),
  joinedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const StringFilterSchema: z.ZodType<Prisma.StringFilter> = z.object({
  equals: z.string().optional(),
  in: z.string().array().optional(),
  notIn: z.string().array().optional(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringFilterSchema) ]).optional(),
}).strict();

export const StringNullableFilterSchema: z.ZodType<Prisma.StringNullableFilter> = z.object({
  equals: z.string().optional().nullable(),
  in: z.string().array().optional().nullable(),
  notIn: z.string().array().optional().nullable(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringNullableFilterSchema) ]).optional().nullable(),
}).strict();

export const JsonNullableFilterSchema: z.ZodType<Prisma.JsonNullableFilter> = z.object({
  equals: InputJsonValueSchema.optional(),
  path: z.string().array().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  string_contains: z.string().optional(),
  string_starts_with: z.string().optional(),
  string_ends_with: z.string().optional(),
  array_starts_with: InputJsonValueSchema.optional().nullable(),
  array_ends_with: InputJsonValueSchema.optional().nullable(),
  array_contains: InputJsonValueSchema.optional().nullable(),
  lt: InputJsonValueSchema.optional(),
  lte: InputJsonValueSchema.optional(),
  gt: InputJsonValueSchema.optional(),
  gte: InputJsonValueSchema.optional(),
  not: InputJsonValueSchema.optional()
}).strict();

export const DateTimeFilterSchema: z.ZodType<Prisma.DateTimeFilter> = z.object({
  equals: z.coerce.date().optional(),
  in: z.coerce.date().array().optional(),
  notIn: z.coerce.date().array().optional(),
  lt: z.coerce.date().optional(),
  lte: z.coerce.date().optional(),
  gt: z.coerce.date().optional(),
  gte: z.coerce.date().optional(),
  not: z.union([ z.coerce.date(),z.lazy(() => NestedDateTimeFilterSchema) ]).optional(),
}).strict();

export const SortOrderInputSchema: z.ZodType<Prisma.SortOrderInput> = z.object({
  sort: z.lazy(() => SortOrderSchema),
  nulls: z.lazy(() => NullsOrderSchema).optional()
}).strict();

export const TempEmbeddingsTableCountOrderByAggregateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  text: z.lazy(() => SortOrderSchema).optional(),
  metadata: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TempEmbeddingsTableMaxOrderByAggregateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  text: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TempEmbeddingsTableMinOrderByAggregateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  text: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const StringWithAggregatesFilterSchema: z.ZodType<Prisma.StringWithAggregatesFilter> = z.object({
  equals: z.string().optional(),
  in: z.string().array().optional(),
  notIn: z.string().array().optional(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedStringFilterSchema).optional(),
  _max: z.lazy(() => NestedStringFilterSchema).optional()
}).strict();

export const StringNullableWithAggregatesFilterSchema: z.ZodType<Prisma.StringNullableWithAggregatesFilter> = z.object({
  equals: z.string().optional().nullable(),
  in: z.string().array().optional().nullable(),
  notIn: z.string().array().optional().nullable(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringNullableWithAggregatesFilterSchema) ]).optional().nullable(),
  _count: z.lazy(() => NestedIntNullableFilterSchema).optional(),
  _min: z.lazy(() => NestedStringNullableFilterSchema).optional(),
  _max: z.lazy(() => NestedStringNullableFilterSchema).optional()
}).strict();

export const JsonNullableWithAggregatesFilterSchema: z.ZodType<Prisma.JsonNullableWithAggregatesFilter> = z.object({
  equals: InputJsonValueSchema.optional(),
  path: z.string().array().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  string_contains: z.string().optional(),
  string_starts_with: z.string().optional(),
  string_ends_with: z.string().optional(),
  array_starts_with: InputJsonValueSchema.optional().nullable(),
  array_ends_with: InputJsonValueSchema.optional().nullable(),
  array_contains: InputJsonValueSchema.optional().nullable(),
  lt: InputJsonValueSchema.optional(),
  lte: InputJsonValueSchema.optional(),
  gt: InputJsonValueSchema.optional(),
  gte: InputJsonValueSchema.optional(),
  not: InputJsonValueSchema.optional(),
  _count: z.lazy(() => NestedIntNullableFilterSchema).optional(),
  _min: z.lazy(() => NestedJsonNullableFilterSchema).optional(),
  _max: z.lazy(() => NestedJsonNullableFilterSchema).optional()
}).strict();

export const DateTimeWithAggregatesFilterSchema: z.ZodType<Prisma.DateTimeWithAggregatesFilter> = z.object({
  equals: z.coerce.date().optional(),
  in: z.coerce.date().array().optional(),
  notIn: z.coerce.date().array().optional(),
  lt: z.coerce.date().optional(),
  lte: z.coerce.date().optional(),
  gt: z.coerce.date().optional(),
  gte: z.coerce.date().optional(),
  not: z.union([ z.coerce.date(),z.lazy(() => NestedDateTimeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedDateTimeFilterSchema).optional(),
  _max: z.lazy(() => NestedDateTimeFilterSchema).optional()
}).strict();

export const KnowledgeNodeEmbeddingsCountOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeEmbeddingsMaxOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeEmbeddingsMinOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const IntFilterSchema: z.ZodType<Prisma.IntFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntFilterSchema) ]).optional(),
}).strict();

export const FooCountOrderByAggregateInputSchema: z.ZodType<Prisma.FooCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooAvgOrderByAggregateInputSchema: z.ZodType<Prisma.FooAvgOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooMaxOrderByAggregateInputSchema: z.ZodType<Prisma.FooMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooMinOrderByAggregateInputSchema: z.ZodType<Prisma.FooMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooSumOrderByAggregateInputSchema: z.ZodType<Prisma.FooSumOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const IntWithAggregatesFilterSchema: z.ZodType<Prisma.IntWithAggregatesFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _avg: z.lazy(() => NestedFloatFilterSchema).optional(),
  _sum: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedIntFilterSchema).optional(),
  _max: z.lazy(() => NestedIntFilterSchema).optional()
}).strict();

export const KnowledgeNodeListRelationFilterSchema: z.ZodType<Prisma.KnowledgeNodeListRelationFilter> = z.object({
  every: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  some: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  none: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const ValidationListRelationFilterSchema: z.ZodType<Prisma.ValidationListRelationFilter> = z.object({
  every: z.lazy(() => ValidationWhereInputSchema).optional(),
  some: z.lazy(() => ValidationWhereInputSchema).optional(),
  none: z.lazy(() => ValidationWhereInputSchema).optional()
}).strict();

export const KnowledgeNodeOrderByRelationAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const ValidationOrderByRelationAggregateInputSchema: z.ZodType<Prisma.ValidationOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const UserCountOrderByAggregateInputSchema: z.ZodType<Prisma.UserCountOrderByAggregateInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const UserMaxOrderByAggregateInputSchema: z.ZodType<Prisma.UserMaxOrderByAggregateInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const UserMinOrderByAggregateInputSchema: z.ZodType<Prisma.UserMinOrderByAggregateInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumNodeTypeFilterSchema: z.ZodType<Prisma.EnumNodeTypeFilter> = z.object({
  equals: z.lazy(() => NodeTypeSchema).optional(),
  in: z.lazy(() => NodeTypeSchema).array().optional(),
  notIn: z.lazy(() => NodeTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => NestedEnumNodeTypeFilterSchema) ]).optional(),
}).strict();

export const EnumSourceTypeFilterSchema: z.ZodType<Prisma.EnumSourceTypeFilter> = z.object({
  equals: z.lazy(() => SourceTypeSchema).optional(),
  in: z.lazy(() => SourceTypeSchema).array().optional(),
  notIn: z.lazy(() => SourceTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => NestedEnumSourceTypeFilterSchema) ]).optional(),
}).strict();

export const BoolFilterSchema: z.ZodType<Prisma.BoolFilter> = z.object({
  equals: z.boolean().optional(),
  not: z.union([ z.boolean(),z.lazy(() => NestedBoolFilterSchema) ]).optional(),
}).strict();

export const UserScalarRelationFilterSchema: z.ZodType<Prisma.UserScalarRelationFilter> = z.object({
  is: z.lazy(() => UserWhereInputSchema).optional(),
  isNot: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export const NodeRelationshipListRelationFilterSchema: z.ZodType<Prisma.NodeRelationshipListRelationFilter> = z.object({
  every: z.lazy(() => NodeRelationshipWhereInputSchema).optional(),
  some: z.lazy(() => NodeRelationshipWhereInputSchema).optional(),
  none: z.lazy(() => NodeRelationshipWhereInputSchema).optional()
}).strict();

export const TopicListRelationFilterSchema: z.ZodType<Prisma.TopicListRelationFilter> = z.object({
  every: z.lazy(() => TopicWhereInputSchema).optional(),
  some: z.lazy(() => TopicWhereInputSchema).optional(),
  none: z.lazy(() => TopicWhereInputSchema).optional()
}).strict();

export const NodeRelationshipOrderByRelationAggregateInputSchema: z.ZodType<Prisma.NodeRelationshipOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TopicOrderByRelationAggregateInputSchema: z.ZodType<Prisma.TopicOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeCountOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  context: z.lazy(() => SortOrderSchema).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeMaxOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeMinOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumNodeTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumNodeTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => NodeTypeSchema).optional(),
  in: z.lazy(() => NodeTypeSchema).array().optional(),
  notIn: z.lazy(() => NodeTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => NestedEnumNodeTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumNodeTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumNodeTypeFilterSchema).optional()
}).strict();

export const EnumSourceTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumSourceTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SourceTypeSchema).optional(),
  in: z.lazy(() => SourceTypeSchema).array().optional(),
  notIn: z.lazy(() => SourceTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => NestedEnumSourceTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSourceTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSourceTypeFilterSchema).optional()
}).strict();

export const BoolWithAggregatesFilterSchema: z.ZodType<Prisma.BoolWithAggregatesFilter> = z.object({
  equals: z.boolean().optional(),
  not: z.union([ z.boolean(),z.lazy(() => NestedBoolWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedBoolFilterSchema).optional(),
  _max: z.lazy(() => NestedBoolFilterSchema).optional()
}).strict();

export const EnumRelationshipTypeFilterSchema: z.ZodType<Prisma.EnumRelationshipTypeFilter> = z.object({
  equals: z.lazy(() => RelationshipTypeSchema).optional(),
  in: z.lazy(() => RelationshipTypeSchema).array().optional(),
  notIn: z.lazy(() => RelationshipTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => NestedEnumRelationshipTypeFilterSchema) ]).optional(),
}).strict();

export const KnowledgeNodeScalarRelationFilterSchema: z.ZodType<Prisma.KnowledgeNodeScalarRelationFilter> = z.object({
  is: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  isNot: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const NodeRelationshipCountOrderByAggregateInputSchema: z.ZodType<Prisma.NodeRelationshipCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const NodeRelationshipMaxOrderByAggregateInputSchema: z.ZodType<Prisma.NodeRelationshipMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const NodeRelationshipMinOrderByAggregateInputSchema: z.ZodType<Prisma.NodeRelationshipMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumRelationshipTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumRelationshipTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => RelationshipTypeSchema).optional(),
  in: z.lazy(() => RelationshipTypeSchema).array().optional(),
  notIn: z.lazy(() => RelationshipTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => NestedEnumRelationshipTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumRelationshipTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumRelationshipTypeFilterSchema).optional()
}).strict();

export const ValidationCountOrderByAggregateInputSchema: z.ZodType<Prisma.ValidationCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const ValidationMaxOrderByAggregateInputSchema: z.ZodType<Prisma.ValidationMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const ValidationMinOrderByAggregateInputSchema: z.ZodType<Prisma.ValidationMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TopicCountOrderByAggregateInputSchema: z.ZodType<Prisma.TopicCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TopicMaxOrderByAggregateInputSchema: z.ZodType<Prisma.TopicMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TopicMinOrderByAggregateInputSchema: z.ZodType<Prisma.TopicMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const QnAPairListRelationFilterSchema: z.ZodType<Prisma.QnAPairListRelationFilter> = z.object({
  every: z.lazy(() => QnAPairWhereInputSchema).optional(),
  some: z.lazy(() => QnAPairWhereInputSchema).optional(),
  none: z.lazy(() => QnAPairWhereInputSchema).optional()
}).strict();

export const SessionListRelationFilterSchema: z.ZodType<Prisma.SessionListRelationFilter> = z.object({
  every: z.lazy(() => SessionWhereInputSchema).optional(),
  some: z.lazy(() => SessionWhereInputSchema).optional(),
  none: z.lazy(() => SessionWhereInputSchema).optional()
}).strict();

export const QnAPairOrderByRelationAggregateInputSchema: z.ZodType<Prisma.QnAPairOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionOrderByRelationAggregateInputSchema: z.ZodType<Prisma.SessionOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const AgentCountOrderByAggregateInputSchema: z.ZodType<Prisma.AgentCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  description: z.lazy(() => SortOrderSchema).optional(),
  povSummary: z.lazy(() => SortOrderSchema).optional(),
  ownerId: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const AgentMaxOrderByAggregateInputSchema: z.ZodType<Prisma.AgentMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  description: z.lazy(() => SortOrderSchema).optional(),
  povSummary: z.lazy(() => SortOrderSchema).optional(),
  ownerId: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const AgentMinOrderByAggregateInputSchema: z.ZodType<Prisma.AgentMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  description: z.lazy(() => SortOrderSchema).optional(),
  povSummary: z.lazy(() => SortOrderSchema).optional(),
  ownerId: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumPersonaTypeFilterSchema: z.ZodType<Prisma.EnumPersonaTypeFilter> = z.object({
  equals: z.lazy(() => PersonaTypeSchema).optional(),
  in: z.lazy(() => PersonaTypeSchema).array().optional(),
  notIn: z.lazy(() => PersonaTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => NestedEnumPersonaTypeFilterSchema) ]).optional(),
}).strict();

export const AgentScalarRelationFilterSchema: z.ZodType<Prisma.AgentScalarRelationFilter> = z.object({
  is: z.lazy(() => AgentWhereInputSchema).optional(),
  isNot: z.lazy(() => AgentWhereInputSchema).optional()
}).strict();

export const QnAPairCountOrderByAggregateInputSchema: z.ZodType<Prisma.QnAPairCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  question: z.lazy(() => SortOrderSchema).optional(),
  answer: z.lazy(() => SortOrderSchema).optional(),
  persona: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const QnAPairMaxOrderByAggregateInputSchema: z.ZodType<Prisma.QnAPairMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  question: z.lazy(() => SortOrderSchema).optional(),
  answer: z.lazy(() => SortOrderSchema).optional(),
  persona: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const QnAPairMinOrderByAggregateInputSchema: z.ZodType<Prisma.QnAPairMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  question: z.lazy(() => SortOrderSchema).optional(),
  answer: z.lazy(() => SortOrderSchema).optional(),
  persona: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumPersonaTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumPersonaTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => PersonaTypeSchema).optional(),
  in: z.lazy(() => PersonaTypeSchema).array().optional(),
  notIn: z.lazy(() => PersonaTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => NestedEnumPersonaTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumPersonaTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumPersonaTypeFilterSchema).optional()
}).strict();

export const QnAPairEmbeddingCountOrderByAggregateInputSchema: z.ZodType<Prisma.QnAPairEmbeddingCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  qnaPairId: z.lazy(() => SortOrderSchema).optional(),
  model: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const QnAPairEmbeddingMaxOrderByAggregateInputSchema: z.ZodType<Prisma.QnAPairEmbeddingMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  qnaPairId: z.lazy(() => SortOrderSchema).optional(),
  model: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const QnAPairEmbeddingMinOrderByAggregateInputSchema: z.ZodType<Prisma.QnAPairEmbeddingMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  qnaPairId: z.lazy(() => SortOrderSchema).optional(),
  model: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumSessionStatusFilterSchema: z.ZodType<Prisma.EnumSessionStatusFilter> = z.object({
  equals: z.lazy(() => SessionStatusSchema).optional(),
  in: z.lazy(() => SessionStatusSchema).array().optional(),
  notIn: z.lazy(() => SessionStatusSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => NestedEnumSessionStatusFilterSchema) ]).optional(),
}).strict();

export const EnumSessionTypeFilterSchema: z.ZodType<Prisma.EnumSessionTypeFilter> = z.object({
  equals: z.lazy(() => SessionTypeSchema).optional(),
  in: z.lazy(() => SessionTypeSchema).array().optional(),
  notIn: z.lazy(() => SessionTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => NestedEnumSessionTypeFilterSchema) ]).optional(),
}).strict();

export const SessionMessageListRelationFilterSchema: z.ZodType<Prisma.SessionMessageListRelationFilter> = z.object({
  every: z.lazy(() => SessionMessageWhereInputSchema).optional(),
  some: z.lazy(() => SessionMessageWhereInputSchema).optional(),
  none: z.lazy(() => SessionMessageWhereInputSchema).optional()
}).strict();

export const SessionParticipantListRelationFilterSchema: z.ZodType<Prisma.SessionParticipantListRelationFilter> = z.object({
  every: z.lazy(() => SessionParticipantWhereInputSchema).optional(),
  some: z.lazy(() => SessionParticipantWhereInputSchema).optional(),
  none: z.lazy(() => SessionParticipantWhereInputSchema).optional()
}).strict();

export const SessionMessageOrderByRelationAggregateInputSchema: z.ZodType<Prisma.SessionMessageOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionParticipantOrderByRelationAggregateInputSchema: z.ZodType<Prisma.SessionParticipantOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionCountOrderByAggregateInputSchema: z.ZodType<Prisma.SessionCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  status: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionMaxOrderByAggregateInputSchema: z.ZodType<Prisma.SessionMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  status: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionMinOrderByAggregateInputSchema: z.ZodType<Prisma.SessionMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  agentId: z.lazy(() => SortOrderSchema).optional(),
  status: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumSessionStatusWithAggregatesFilterSchema: z.ZodType<Prisma.EnumSessionStatusWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SessionStatusSchema).optional(),
  in: z.lazy(() => SessionStatusSchema).array().optional(),
  notIn: z.lazy(() => SessionStatusSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => NestedEnumSessionStatusWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSessionStatusFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSessionStatusFilterSchema).optional()
}).strict();

export const EnumSessionTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumSessionTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SessionTypeSchema).optional(),
  in: z.lazy(() => SessionTypeSchema).array().optional(),
  notIn: z.lazy(() => SessionTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => NestedEnumSessionTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSessionTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSessionTypeFilterSchema).optional()
}).strict();

export const EnumRoleTypeFilterSchema: z.ZodType<Prisma.EnumRoleTypeFilter> = z.object({
  equals: z.lazy(() => RoleTypeSchema).optional(),
  in: z.lazy(() => RoleTypeSchema).array().optional(),
  notIn: z.lazy(() => RoleTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => NestedEnumRoleTypeFilterSchema) ]).optional(),
}).strict();

export const SessionScalarRelationFilterSchema: z.ZodType<Prisma.SessionScalarRelationFilter> = z.object({
  is: z.lazy(() => SessionWhereInputSchema).optional(),
  isNot: z.lazy(() => SessionWhereInputSchema).optional()
}).strict();

export const SessionMessageCountOrderByAggregateInputSchema: z.ZodType<Prisma.SessionMessageCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  senderId: z.lazy(() => SortOrderSchema).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionMessageMaxOrderByAggregateInputSchema: z.ZodType<Prisma.SessionMessageMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  senderId: z.lazy(() => SortOrderSchema).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionMessageMinOrderByAggregateInputSchema: z.ZodType<Prisma.SessionMessageMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  senderId: z.lazy(() => SortOrderSchema).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumRoleTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumRoleTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => RoleTypeSchema).optional(),
  in: z.lazy(() => RoleTypeSchema).array().optional(),
  notIn: z.lazy(() => RoleTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => NestedEnumRoleTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumRoleTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumRoleTypeFilterSchema).optional()
}).strict();

export const EnumSessionRoleFilterSchema: z.ZodType<Prisma.EnumSessionRoleFilter> = z.object({
  equals: z.lazy(() => SessionRoleSchema).optional(),
  in: z.lazy(() => SessionRoleSchema).array().optional(),
  notIn: z.lazy(() => SessionRoleSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => NestedEnumSessionRoleFilterSchema) ]).optional(),
}).strict();

export const SessionParticipantCountOrderByAggregateInputSchema: z.ZodType<Prisma.SessionParticipantCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  userId: z.lazy(() => SortOrderSchema).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  joinedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionParticipantMaxOrderByAggregateInputSchema: z.ZodType<Prisma.SessionParticipantMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  userId: z.lazy(() => SortOrderSchema).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  joinedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const SessionParticipantMinOrderByAggregateInputSchema: z.ZodType<Prisma.SessionParticipantMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  sessionId: z.lazy(() => SortOrderSchema).optional(),
  userId: z.lazy(() => SortOrderSchema).optional(),
  role: z.lazy(() => SortOrderSchema).optional(),
  joinedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumSessionRoleWithAggregatesFilterSchema: z.ZodType<Prisma.EnumSessionRoleWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SessionRoleSchema).optional(),
  in: z.lazy(() => SessionRoleSchema).array().optional(),
  notIn: z.lazy(() => SessionRoleSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => NestedEnumSessionRoleWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSessionRoleFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSessionRoleFilterSchema).optional()
}).strict();

export const StringFieldUpdateOperationsInputSchema: z.ZodType<Prisma.StringFieldUpdateOperationsInput> = z.object({
  set: z.string().optional()
}).strict();

export const NullableStringFieldUpdateOperationsInputSchema: z.ZodType<Prisma.NullableStringFieldUpdateOperationsInput> = z.object({
  set: z.string().optional().nullable()
}).strict();

export const DateTimeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.DateTimeFieldUpdateOperationsInput> = z.object({
  set: z.coerce.date().optional()
}).strict();

export const IntFieldUpdateOperationsInputSchema: z.ZodType<Prisma.IntFieldUpdateOperationsInput> = z.object({
  set: z.number().optional(),
  increment: z.number().optional(),
  decrement: z.number().optional(),
  multiply: z.number().optional(),
  divide: z.number().optional()
}).strict();

export const KnowledgeNodeCreateNestedManyWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedManyWithoutAuthorInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema).array() ]).optional(),
  createMany: z.lazy(() => KnowledgeNodeCreateManyAuthorInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const ValidationCreateNestedManyWithoutExpertInputSchema: z.ZodType<Prisma.ValidationCreateNestedManyWithoutExpertInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationCreateWithoutExpertInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyExpertInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedCreateNestedManyWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateNestedManyWithoutAuthorInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema).array() ]).optional(),
  createMany: z.lazy(() => KnowledgeNodeCreateManyAuthorInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const ValidationUncheckedCreateNestedManyWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateNestedManyWithoutExpertInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationCreateWithoutExpertInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyExpertInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUpdateManyWithoutAuthorNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyWithoutAuthorNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema).array() ]).optional(),
  createMany: z.lazy(() => KnowledgeNodeCreateManyAuthorInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const ValidationUpdateManyWithoutExpertNestedInputSchema: z.ZodType<Prisma.ValidationUpdateManyWithoutExpertNestedInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationCreateWithoutExpertInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => ValidationUpsertWithWhereUniqueWithoutExpertInputSchema),z.lazy(() => ValidationUpsertWithWhereUniqueWithoutExpertInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyExpertInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => ValidationUpdateWithWhereUniqueWithoutExpertInputSchema),z.lazy(() => ValidationUpdateWithWhereUniqueWithoutExpertInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => ValidationUpdateManyWithWhereWithoutExpertInputSchema),z.lazy(() => ValidationUpdateManyWithWhereWithoutExpertInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedUpdateManyWithoutAuthorNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyWithoutAuthorNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema).array() ]).optional(),
  createMany: z.lazy(() => KnowledgeNodeCreateManyAuthorInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyWithoutExpertNestedInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyWithoutExpertNestedInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationCreateWithoutExpertInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => ValidationUpsertWithWhereUniqueWithoutExpertInputSchema),z.lazy(() => ValidationUpsertWithWhereUniqueWithoutExpertInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyExpertInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => ValidationUpdateWithWhereUniqueWithoutExpertInputSchema),z.lazy(() => ValidationUpdateWithWhereUniqueWithoutExpertInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => ValidationUpdateManyWithWhereWithoutExpertInputSchema),z.lazy(() => ValidationUpdateManyWithWhereWithoutExpertInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const UserCreateNestedOneWithoutNodesInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutNodesInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutNodesInputSchema),z.lazy(() => UserUncheckedCreateWithoutNodesInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutNodesInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional()
}).strict();

export const NodeRelationshipCreateNestedManyWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipCreateNestedManyWithoutFromInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyFromInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipCreateNestedManyWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipCreateNestedManyWithoutToInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateWithoutToInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyToInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const ValidationCreateNestedManyWithoutNodeInputSchema: z.ZodType<Prisma.ValidationCreateNestedManyWithoutNodeInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationCreateWithoutNodeInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyNodeInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const TopicCreateNestedManyWithoutNodesInputSchema: z.ZodType<Prisma.TopicCreateNestedManyWithoutNodesInput> = z.object({
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicCreateWithoutNodesInputSchema).array(),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema),z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateNestedManyWithoutFromInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyFromInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateNestedManyWithoutToInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateWithoutToInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyToInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const ValidationUncheckedCreateNestedManyWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateNestedManyWithoutNodeInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationCreateWithoutNodeInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyNodeInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const TopicUncheckedCreateNestedManyWithoutNodesInputSchema: z.ZodType<Prisma.TopicUncheckedCreateNestedManyWithoutNodesInput> = z.object({
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicCreateWithoutNodesInputSchema).array(),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema),z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const EnumNodeTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumNodeTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => NodeTypeSchema).optional()
}).strict();

export const EnumSourceTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumSourceTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => SourceTypeSchema).optional()
}).strict();

export const BoolFieldUpdateOperationsInputSchema: z.ZodType<Prisma.BoolFieldUpdateOperationsInput> = z.object({
  set: z.boolean().optional()
}).strict();

export const UserUpdateOneRequiredWithoutNodesNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutNodesNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutNodesInputSchema),z.lazy(() => UserUncheckedCreateWithoutNodesInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutNodesInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutNodesInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutNodesInputSchema),z.lazy(() => UserUpdateWithoutNodesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNodesInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUpdateManyWithoutFromNestedInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyWithoutFromNestedInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyFromInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUpdateManyWithoutToNestedInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyWithoutToNestedInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateWithoutToInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema),z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyToInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema),z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutToInputSchema),z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutToInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const ValidationUpdateManyWithoutNodeNestedInputSchema: z.ZodType<Prisma.ValidationUpdateManyWithoutNodeNestedInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationCreateWithoutNodeInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => ValidationUpsertWithWhereUniqueWithoutNodeInputSchema),z.lazy(() => ValidationUpsertWithWhereUniqueWithoutNodeInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyNodeInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => ValidationUpdateWithWhereUniqueWithoutNodeInputSchema),z.lazy(() => ValidationUpdateWithWhereUniqueWithoutNodeInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => ValidationUpdateManyWithWhereWithoutNodeInputSchema),z.lazy(() => ValidationUpdateManyWithWhereWithoutNodeInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const TopicUpdateManyWithoutNodesNestedInputSchema: z.ZodType<Prisma.TopicUpdateManyWithoutNodesNestedInput> = z.object({
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicCreateWithoutNodesInputSchema).array(),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema),z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => TopicUpsertWithWhereUniqueWithoutNodesInputSchema),z.lazy(() => TopicUpsertWithWhereUniqueWithoutNodesInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => TopicUpdateWithWhereUniqueWithoutNodesInputSchema),z.lazy(() => TopicUpdateWithWhereUniqueWithoutNodesInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => TopicUpdateManyWithWhereWithoutNodesInputSchema),z.lazy(() => TopicUpdateManyWithWhereWithoutNodesInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => TopicScalarWhereInputSchema),z.lazy(() => TopicScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyWithoutFromNestedInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyFromInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyWithoutToNestedInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateWithoutToInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema),z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyToInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema),z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutToInputSchema),z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutToInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyWithoutNodeNestedInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationCreateWithoutNodeInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => ValidationUpsertWithWhereUniqueWithoutNodeInputSchema),z.lazy(() => ValidationUpsertWithWhereUniqueWithoutNodeInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyNodeInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => ValidationUpdateWithWhereUniqueWithoutNodeInputSchema),z.lazy(() => ValidationUpdateWithWhereUniqueWithoutNodeInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => ValidationUpdateManyWithWhereWithoutNodeInputSchema),z.lazy(() => ValidationUpdateManyWithWhereWithoutNodeInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const TopicUncheckedUpdateManyWithoutNodesNestedInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateManyWithoutNodesNestedInput> = z.object({
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicCreateWithoutNodesInputSchema).array(),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema),z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => TopicUpsertWithWhereUniqueWithoutNodesInputSchema),z.lazy(() => TopicUpsertWithWhereUniqueWithoutNodesInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => TopicUpdateWithWhereUniqueWithoutNodesInputSchema),z.lazy(() => TopicUpdateWithWhereUniqueWithoutNodesInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => TopicUpdateManyWithWhereWithoutNodesInputSchema),z.lazy(() => TopicUpdateManyWithWhereWithoutNodesInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => TopicScalarWhereInputSchema),z.lazy(() => TopicScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeCreateNestedOneWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedOneWithoutRelationshipsFromInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutRelationshipsFromInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateNestedOneWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedOneWithoutRelationshipsToInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutRelationshipsToInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional()
}).strict();

export const EnumRelationshipTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumRelationshipTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => RelationshipTypeSchema).optional()
}).strict();

export const KnowledgeNodeUpdateOneRequiredWithoutRelationshipsFromNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateOneRequiredWithoutRelationshipsFromNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutRelationshipsFromInputSchema).optional(),
  upsert: z.lazy(() => KnowledgeNodeUpsertWithoutRelationshipsFromInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeUpdateOneRequiredWithoutRelationshipsToNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateOneRequiredWithoutRelationshipsToNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutRelationshipsToInputSchema).optional(),
  upsert: z.lazy(() => KnowledgeNodeUpsertWithoutRelationshipsToInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeCreateNestedOneWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedOneWithoutValidationsInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutValidationsInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional()
}).strict();

export const UserCreateNestedOneWithoutValidationsInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutValidationsInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutValidationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutValidationsInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateOneRequiredWithoutValidationsNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateOneRequiredWithoutValidationsNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutValidationsInputSchema).optional(),
  upsert: z.lazy(() => KnowledgeNodeUpsertWithoutValidationsInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateToOneWithWhereWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUpdateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutValidationsInputSchema) ]).optional(),
}).strict();

export const UserUpdateOneRequiredWithoutValidationsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutValidationsNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutValidationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutValidationsInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutValidationsInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutValidationsInputSchema),z.lazy(() => UserUpdateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutValidationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeCreateNestedManyWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedManyWithoutTopicsInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedCreateNestedManyWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateNestedManyWithoutTopicsInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUpdateManyWithoutTopicsNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyWithoutTopicsNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedUpdateManyWithoutTopicsNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyWithoutTopicsNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const QnAPairCreateNestedManyWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairCreateNestedManyWithoutAgentInput> = z.object({
  create: z.union([ z.lazy(() => QnAPairCreateWithoutAgentInputSchema),z.lazy(() => QnAPairCreateWithoutAgentInputSchema).array(),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => QnAPairCreateOrConnectWithoutAgentInputSchema),z.lazy(() => QnAPairCreateOrConnectWithoutAgentInputSchema).array() ]).optional(),
  createMany: z.lazy(() => QnAPairCreateManyAgentInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const SessionCreateNestedManyWithoutAgentInputSchema: z.ZodType<Prisma.SessionCreateNestedManyWithoutAgentInput> = z.object({
  create: z.union([ z.lazy(() => SessionCreateWithoutAgentInputSchema),z.lazy(() => SessionCreateWithoutAgentInputSchema).array(),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionCreateOrConnectWithoutAgentInputSchema),z.lazy(() => SessionCreateOrConnectWithoutAgentInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionCreateManyAgentInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const QnAPairUncheckedCreateNestedManyWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairUncheckedCreateNestedManyWithoutAgentInput> = z.object({
  create: z.union([ z.lazy(() => QnAPairCreateWithoutAgentInputSchema),z.lazy(() => QnAPairCreateWithoutAgentInputSchema).array(),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => QnAPairCreateOrConnectWithoutAgentInputSchema),z.lazy(() => QnAPairCreateOrConnectWithoutAgentInputSchema).array() ]).optional(),
  createMany: z.lazy(() => QnAPairCreateManyAgentInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const SessionUncheckedCreateNestedManyWithoutAgentInputSchema: z.ZodType<Prisma.SessionUncheckedCreateNestedManyWithoutAgentInput> = z.object({
  create: z.union([ z.lazy(() => SessionCreateWithoutAgentInputSchema),z.lazy(() => SessionCreateWithoutAgentInputSchema).array(),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionCreateOrConnectWithoutAgentInputSchema),z.lazy(() => SessionCreateOrConnectWithoutAgentInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionCreateManyAgentInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const QnAPairUpdateManyWithoutAgentNestedInputSchema: z.ZodType<Prisma.QnAPairUpdateManyWithoutAgentNestedInput> = z.object({
  create: z.union([ z.lazy(() => QnAPairCreateWithoutAgentInputSchema),z.lazy(() => QnAPairCreateWithoutAgentInputSchema).array(),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => QnAPairCreateOrConnectWithoutAgentInputSchema),z.lazy(() => QnAPairCreateOrConnectWithoutAgentInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => QnAPairUpsertWithWhereUniqueWithoutAgentInputSchema),z.lazy(() => QnAPairUpsertWithWhereUniqueWithoutAgentInputSchema).array() ]).optional(),
  createMany: z.lazy(() => QnAPairCreateManyAgentInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => QnAPairUpdateWithWhereUniqueWithoutAgentInputSchema),z.lazy(() => QnAPairUpdateWithWhereUniqueWithoutAgentInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => QnAPairUpdateManyWithWhereWithoutAgentInputSchema),z.lazy(() => QnAPairUpdateManyWithWhereWithoutAgentInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => QnAPairScalarWhereInputSchema),z.lazy(() => QnAPairScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const SessionUpdateManyWithoutAgentNestedInputSchema: z.ZodType<Prisma.SessionUpdateManyWithoutAgentNestedInput> = z.object({
  create: z.union([ z.lazy(() => SessionCreateWithoutAgentInputSchema),z.lazy(() => SessionCreateWithoutAgentInputSchema).array(),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionCreateOrConnectWithoutAgentInputSchema),z.lazy(() => SessionCreateOrConnectWithoutAgentInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => SessionUpsertWithWhereUniqueWithoutAgentInputSchema),z.lazy(() => SessionUpsertWithWhereUniqueWithoutAgentInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionCreateManyAgentInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => SessionUpdateWithWhereUniqueWithoutAgentInputSchema),z.lazy(() => SessionUpdateWithWhereUniqueWithoutAgentInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => SessionUpdateManyWithWhereWithoutAgentInputSchema),z.lazy(() => SessionUpdateManyWithWhereWithoutAgentInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => SessionScalarWhereInputSchema),z.lazy(() => SessionScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const QnAPairUncheckedUpdateManyWithoutAgentNestedInputSchema: z.ZodType<Prisma.QnAPairUncheckedUpdateManyWithoutAgentNestedInput> = z.object({
  create: z.union([ z.lazy(() => QnAPairCreateWithoutAgentInputSchema),z.lazy(() => QnAPairCreateWithoutAgentInputSchema).array(),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => QnAPairCreateOrConnectWithoutAgentInputSchema),z.lazy(() => QnAPairCreateOrConnectWithoutAgentInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => QnAPairUpsertWithWhereUniqueWithoutAgentInputSchema),z.lazy(() => QnAPairUpsertWithWhereUniqueWithoutAgentInputSchema).array() ]).optional(),
  createMany: z.lazy(() => QnAPairCreateManyAgentInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => QnAPairWhereUniqueInputSchema),z.lazy(() => QnAPairWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => QnAPairUpdateWithWhereUniqueWithoutAgentInputSchema),z.lazy(() => QnAPairUpdateWithWhereUniqueWithoutAgentInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => QnAPairUpdateManyWithWhereWithoutAgentInputSchema),z.lazy(() => QnAPairUpdateManyWithWhereWithoutAgentInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => QnAPairScalarWhereInputSchema),z.lazy(() => QnAPairScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const SessionUncheckedUpdateManyWithoutAgentNestedInputSchema: z.ZodType<Prisma.SessionUncheckedUpdateManyWithoutAgentNestedInput> = z.object({
  create: z.union([ z.lazy(() => SessionCreateWithoutAgentInputSchema),z.lazy(() => SessionCreateWithoutAgentInputSchema).array(),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionCreateOrConnectWithoutAgentInputSchema),z.lazy(() => SessionCreateOrConnectWithoutAgentInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => SessionUpsertWithWhereUniqueWithoutAgentInputSchema),z.lazy(() => SessionUpsertWithWhereUniqueWithoutAgentInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionCreateManyAgentInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => SessionWhereUniqueInputSchema),z.lazy(() => SessionWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => SessionUpdateWithWhereUniqueWithoutAgentInputSchema),z.lazy(() => SessionUpdateWithWhereUniqueWithoutAgentInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => SessionUpdateManyWithWhereWithoutAgentInputSchema),z.lazy(() => SessionUpdateManyWithWhereWithoutAgentInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => SessionScalarWhereInputSchema),z.lazy(() => SessionScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const AgentCreateNestedOneWithoutQnaPairsInputSchema: z.ZodType<Prisma.AgentCreateNestedOneWithoutQnaPairsInput> = z.object({
  create: z.union([ z.lazy(() => AgentCreateWithoutQnaPairsInputSchema),z.lazy(() => AgentUncheckedCreateWithoutQnaPairsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => AgentCreateOrConnectWithoutQnaPairsInputSchema).optional(),
  connect: z.lazy(() => AgentWhereUniqueInputSchema).optional()
}).strict();

export const EnumPersonaTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumPersonaTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => PersonaTypeSchema).optional()
}).strict();

export const AgentUpdateOneRequiredWithoutQnaPairsNestedInputSchema: z.ZodType<Prisma.AgentUpdateOneRequiredWithoutQnaPairsNestedInput> = z.object({
  create: z.union([ z.lazy(() => AgentCreateWithoutQnaPairsInputSchema),z.lazy(() => AgentUncheckedCreateWithoutQnaPairsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => AgentCreateOrConnectWithoutQnaPairsInputSchema).optional(),
  upsert: z.lazy(() => AgentUpsertWithoutQnaPairsInputSchema).optional(),
  connect: z.lazy(() => AgentWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => AgentUpdateToOneWithWhereWithoutQnaPairsInputSchema),z.lazy(() => AgentUpdateWithoutQnaPairsInputSchema),z.lazy(() => AgentUncheckedUpdateWithoutQnaPairsInputSchema) ]).optional(),
}).strict();

export const AgentCreateNestedOneWithoutSessionsInputSchema: z.ZodType<Prisma.AgentCreateNestedOneWithoutSessionsInput> = z.object({
  create: z.union([ z.lazy(() => AgentCreateWithoutSessionsInputSchema),z.lazy(() => AgentUncheckedCreateWithoutSessionsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => AgentCreateOrConnectWithoutSessionsInputSchema).optional(),
  connect: z.lazy(() => AgentWhereUniqueInputSchema).optional()
}).strict();

export const SessionMessageCreateNestedManyWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageCreateNestedManyWithoutSessionInput> = z.object({
  create: z.union([ z.lazy(() => SessionMessageCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageCreateWithoutSessionInputSchema).array(),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionMessageCreateOrConnectWithoutSessionInputSchema),z.lazy(() => SessionMessageCreateOrConnectWithoutSessionInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionMessageCreateManySessionInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const SessionParticipantCreateNestedManyWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantCreateNestedManyWithoutSessionInput> = z.object({
  create: z.union([ z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema).array(),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionParticipantCreateOrConnectWithoutSessionInputSchema),z.lazy(() => SessionParticipantCreateOrConnectWithoutSessionInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionParticipantCreateManySessionInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const SessionMessageUncheckedCreateNestedManyWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageUncheckedCreateNestedManyWithoutSessionInput> = z.object({
  create: z.union([ z.lazy(() => SessionMessageCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageCreateWithoutSessionInputSchema).array(),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionMessageCreateOrConnectWithoutSessionInputSchema),z.lazy(() => SessionMessageCreateOrConnectWithoutSessionInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionMessageCreateManySessionInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const SessionParticipantUncheckedCreateNestedManyWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantUncheckedCreateNestedManyWithoutSessionInput> = z.object({
  create: z.union([ z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema).array(),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionParticipantCreateOrConnectWithoutSessionInputSchema),z.lazy(() => SessionParticipantCreateOrConnectWithoutSessionInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionParticipantCreateManySessionInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const EnumSessionStatusFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumSessionStatusFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => SessionStatusSchema).optional()
}).strict();

export const EnumSessionTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumSessionTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => SessionTypeSchema).optional()
}).strict();

export const AgentUpdateOneRequiredWithoutSessionsNestedInputSchema: z.ZodType<Prisma.AgentUpdateOneRequiredWithoutSessionsNestedInput> = z.object({
  create: z.union([ z.lazy(() => AgentCreateWithoutSessionsInputSchema),z.lazy(() => AgentUncheckedCreateWithoutSessionsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => AgentCreateOrConnectWithoutSessionsInputSchema).optional(),
  upsert: z.lazy(() => AgentUpsertWithoutSessionsInputSchema).optional(),
  connect: z.lazy(() => AgentWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => AgentUpdateToOneWithWhereWithoutSessionsInputSchema),z.lazy(() => AgentUpdateWithoutSessionsInputSchema),z.lazy(() => AgentUncheckedUpdateWithoutSessionsInputSchema) ]).optional(),
}).strict();

export const SessionMessageUpdateManyWithoutSessionNestedInputSchema: z.ZodType<Prisma.SessionMessageUpdateManyWithoutSessionNestedInput> = z.object({
  create: z.union([ z.lazy(() => SessionMessageCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageCreateWithoutSessionInputSchema).array(),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionMessageCreateOrConnectWithoutSessionInputSchema),z.lazy(() => SessionMessageCreateOrConnectWithoutSessionInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => SessionMessageUpsertWithWhereUniqueWithoutSessionInputSchema),z.lazy(() => SessionMessageUpsertWithWhereUniqueWithoutSessionInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionMessageCreateManySessionInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => SessionMessageUpdateWithWhereUniqueWithoutSessionInputSchema),z.lazy(() => SessionMessageUpdateWithWhereUniqueWithoutSessionInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => SessionMessageUpdateManyWithWhereWithoutSessionInputSchema),z.lazy(() => SessionMessageUpdateManyWithWhereWithoutSessionInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => SessionMessageScalarWhereInputSchema),z.lazy(() => SessionMessageScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const SessionParticipantUpdateManyWithoutSessionNestedInputSchema: z.ZodType<Prisma.SessionParticipantUpdateManyWithoutSessionNestedInput> = z.object({
  create: z.union([ z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema).array(),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionParticipantCreateOrConnectWithoutSessionInputSchema),z.lazy(() => SessionParticipantCreateOrConnectWithoutSessionInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => SessionParticipantUpsertWithWhereUniqueWithoutSessionInputSchema),z.lazy(() => SessionParticipantUpsertWithWhereUniqueWithoutSessionInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionParticipantCreateManySessionInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => SessionParticipantUpdateWithWhereUniqueWithoutSessionInputSchema),z.lazy(() => SessionParticipantUpdateWithWhereUniqueWithoutSessionInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => SessionParticipantUpdateManyWithWhereWithoutSessionInputSchema),z.lazy(() => SessionParticipantUpdateManyWithWhereWithoutSessionInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => SessionParticipantScalarWhereInputSchema),z.lazy(() => SessionParticipantScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const SessionMessageUncheckedUpdateManyWithoutSessionNestedInputSchema: z.ZodType<Prisma.SessionMessageUncheckedUpdateManyWithoutSessionNestedInput> = z.object({
  create: z.union([ z.lazy(() => SessionMessageCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageCreateWithoutSessionInputSchema).array(),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionMessageCreateOrConnectWithoutSessionInputSchema),z.lazy(() => SessionMessageCreateOrConnectWithoutSessionInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => SessionMessageUpsertWithWhereUniqueWithoutSessionInputSchema),z.lazy(() => SessionMessageUpsertWithWhereUniqueWithoutSessionInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionMessageCreateManySessionInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => SessionMessageWhereUniqueInputSchema),z.lazy(() => SessionMessageWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => SessionMessageUpdateWithWhereUniqueWithoutSessionInputSchema),z.lazy(() => SessionMessageUpdateWithWhereUniqueWithoutSessionInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => SessionMessageUpdateManyWithWhereWithoutSessionInputSchema),z.lazy(() => SessionMessageUpdateManyWithWhereWithoutSessionInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => SessionMessageScalarWhereInputSchema),z.lazy(() => SessionMessageScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const SessionParticipantUncheckedUpdateManyWithoutSessionNestedInputSchema: z.ZodType<Prisma.SessionParticipantUncheckedUpdateManyWithoutSessionNestedInput> = z.object({
  create: z.union([ z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema).array(),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => SessionParticipantCreateOrConnectWithoutSessionInputSchema),z.lazy(() => SessionParticipantCreateOrConnectWithoutSessionInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => SessionParticipantUpsertWithWhereUniqueWithoutSessionInputSchema),z.lazy(() => SessionParticipantUpsertWithWhereUniqueWithoutSessionInputSchema).array() ]).optional(),
  createMany: z.lazy(() => SessionParticipantCreateManySessionInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => SessionParticipantWhereUniqueInputSchema),z.lazy(() => SessionParticipantWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => SessionParticipantUpdateWithWhereUniqueWithoutSessionInputSchema),z.lazy(() => SessionParticipantUpdateWithWhereUniqueWithoutSessionInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => SessionParticipantUpdateManyWithWhereWithoutSessionInputSchema),z.lazy(() => SessionParticipantUpdateManyWithWhereWithoutSessionInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => SessionParticipantScalarWhereInputSchema),z.lazy(() => SessionParticipantScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const SessionCreateNestedOneWithoutMessagesInputSchema: z.ZodType<Prisma.SessionCreateNestedOneWithoutMessagesInput> = z.object({
  create: z.union([ z.lazy(() => SessionCreateWithoutMessagesInputSchema),z.lazy(() => SessionUncheckedCreateWithoutMessagesInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => SessionCreateOrConnectWithoutMessagesInputSchema).optional(),
  connect: z.lazy(() => SessionWhereUniqueInputSchema).optional()
}).strict();

export const EnumRoleTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumRoleTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => RoleTypeSchema).optional()
}).strict();

export const SessionUpdateOneRequiredWithoutMessagesNestedInputSchema: z.ZodType<Prisma.SessionUpdateOneRequiredWithoutMessagesNestedInput> = z.object({
  create: z.union([ z.lazy(() => SessionCreateWithoutMessagesInputSchema),z.lazy(() => SessionUncheckedCreateWithoutMessagesInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => SessionCreateOrConnectWithoutMessagesInputSchema).optional(),
  upsert: z.lazy(() => SessionUpsertWithoutMessagesInputSchema).optional(),
  connect: z.lazy(() => SessionWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => SessionUpdateToOneWithWhereWithoutMessagesInputSchema),z.lazy(() => SessionUpdateWithoutMessagesInputSchema),z.lazy(() => SessionUncheckedUpdateWithoutMessagesInputSchema) ]).optional(),
}).strict();

export const SessionCreateNestedOneWithoutParticipantsInputSchema: z.ZodType<Prisma.SessionCreateNestedOneWithoutParticipantsInput> = z.object({
  create: z.union([ z.lazy(() => SessionCreateWithoutParticipantsInputSchema),z.lazy(() => SessionUncheckedCreateWithoutParticipantsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => SessionCreateOrConnectWithoutParticipantsInputSchema).optional(),
  connect: z.lazy(() => SessionWhereUniqueInputSchema).optional()
}).strict();

export const EnumSessionRoleFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumSessionRoleFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => SessionRoleSchema).optional()
}).strict();

export const SessionUpdateOneRequiredWithoutParticipantsNestedInputSchema: z.ZodType<Prisma.SessionUpdateOneRequiredWithoutParticipantsNestedInput> = z.object({
  create: z.union([ z.lazy(() => SessionCreateWithoutParticipantsInputSchema),z.lazy(() => SessionUncheckedCreateWithoutParticipantsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => SessionCreateOrConnectWithoutParticipantsInputSchema).optional(),
  upsert: z.lazy(() => SessionUpsertWithoutParticipantsInputSchema).optional(),
  connect: z.lazy(() => SessionWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => SessionUpdateToOneWithWhereWithoutParticipantsInputSchema),z.lazy(() => SessionUpdateWithoutParticipantsInputSchema),z.lazy(() => SessionUncheckedUpdateWithoutParticipantsInputSchema) ]).optional(),
}).strict();

export const NestedStringFilterSchema: z.ZodType<Prisma.NestedStringFilter> = z.object({
  equals: z.string().optional(),
  in: z.string().array().optional(),
  notIn: z.string().array().optional(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringFilterSchema) ]).optional(),
}).strict();

export const NestedStringNullableFilterSchema: z.ZodType<Prisma.NestedStringNullableFilter> = z.object({
  equals: z.string().optional().nullable(),
  in: z.string().array().optional().nullable(),
  notIn: z.string().array().optional().nullable(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringNullableFilterSchema) ]).optional().nullable(),
}).strict();

export const NestedDateTimeFilterSchema: z.ZodType<Prisma.NestedDateTimeFilter> = z.object({
  equals: z.coerce.date().optional(),
  in: z.coerce.date().array().optional(),
  notIn: z.coerce.date().array().optional(),
  lt: z.coerce.date().optional(),
  lte: z.coerce.date().optional(),
  gt: z.coerce.date().optional(),
  gte: z.coerce.date().optional(),
  not: z.union([ z.coerce.date(),z.lazy(() => NestedDateTimeFilterSchema) ]).optional(),
}).strict();

export const NestedStringWithAggregatesFilterSchema: z.ZodType<Prisma.NestedStringWithAggregatesFilter> = z.object({
  equals: z.string().optional(),
  in: z.string().array().optional(),
  notIn: z.string().array().optional(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedStringFilterSchema).optional(),
  _max: z.lazy(() => NestedStringFilterSchema).optional()
}).strict();

export const NestedIntFilterSchema: z.ZodType<Prisma.NestedIntFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntFilterSchema) ]).optional(),
}).strict();

export const NestedStringNullableWithAggregatesFilterSchema: z.ZodType<Prisma.NestedStringNullableWithAggregatesFilter> = z.object({
  equals: z.string().optional().nullable(),
  in: z.string().array().optional().nullable(),
  notIn: z.string().array().optional().nullable(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringNullableWithAggregatesFilterSchema) ]).optional().nullable(),
  _count: z.lazy(() => NestedIntNullableFilterSchema).optional(),
  _min: z.lazy(() => NestedStringNullableFilterSchema).optional(),
  _max: z.lazy(() => NestedStringNullableFilterSchema).optional()
}).strict();

export const NestedIntNullableFilterSchema: z.ZodType<Prisma.NestedIntNullableFilter> = z.object({
  equals: z.number().optional().nullable(),
  in: z.number().array().optional().nullable(),
  notIn: z.number().array().optional().nullable(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntNullableFilterSchema) ]).optional().nullable(),
}).strict();

export const NestedJsonNullableFilterSchema: z.ZodType<Prisma.NestedJsonNullableFilter> = z.object({
  equals: InputJsonValueSchema.optional(),
  path: z.string().array().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  string_contains: z.string().optional(),
  string_starts_with: z.string().optional(),
  string_ends_with: z.string().optional(),
  array_starts_with: InputJsonValueSchema.optional().nullable(),
  array_ends_with: InputJsonValueSchema.optional().nullable(),
  array_contains: InputJsonValueSchema.optional().nullable(),
  lt: InputJsonValueSchema.optional(),
  lte: InputJsonValueSchema.optional(),
  gt: InputJsonValueSchema.optional(),
  gte: InputJsonValueSchema.optional(),
  not: InputJsonValueSchema.optional()
}).strict();

export const NestedDateTimeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedDateTimeWithAggregatesFilter> = z.object({
  equals: z.coerce.date().optional(),
  in: z.coerce.date().array().optional(),
  notIn: z.coerce.date().array().optional(),
  lt: z.coerce.date().optional(),
  lte: z.coerce.date().optional(),
  gt: z.coerce.date().optional(),
  gte: z.coerce.date().optional(),
  not: z.union([ z.coerce.date(),z.lazy(() => NestedDateTimeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedDateTimeFilterSchema).optional(),
  _max: z.lazy(() => NestedDateTimeFilterSchema).optional()
}).strict();

export const NestedIntWithAggregatesFilterSchema: z.ZodType<Prisma.NestedIntWithAggregatesFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _avg: z.lazy(() => NestedFloatFilterSchema).optional(),
  _sum: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedIntFilterSchema).optional(),
  _max: z.lazy(() => NestedIntFilterSchema).optional()
}).strict();

export const NestedFloatFilterSchema: z.ZodType<Prisma.NestedFloatFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedFloatFilterSchema) ]).optional(),
}).strict();

export const NestedEnumNodeTypeFilterSchema: z.ZodType<Prisma.NestedEnumNodeTypeFilter> = z.object({
  equals: z.lazy(() => NodeTypeSchema).optional(),
  in: z.lazy(() => NodeTypeSchema).array().optional(),
  notIn: z.lazy(() => NodeTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => NestedEnumNodeTypeFilterSchema) ]).optional(),
}).strict();

export const NestedEnumSourceTypeFilterSchema: z.ZodType<Prisma.NestedEnumSourceTypeFilter> = z.object({
  equals: z.lazy(() => SourceTypeSchema).optional(),
  in: z.lazy(() => SourceTypeSchema).array().optional(),
  notIn: z.lazy(() => SourceTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => NestedEnumSourceTypeFilterSchema) ]).optional(),
}).strict();

export const NestedBoolFilterSchema: z.ZodType<Prisma.NestedBoolFilter> = z.object({
  equals: z.boolean().optional(),
  not: z.union([ z.boolean(),z.lazy(() => NestedBoolFilterSchema) ]).optional(),
}).strict();

export const NestedEnumNodeTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumNodeTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => NodeTypeSchema).optional(),
  in: z.lazy(() => NodeTypeSchema).array().optional(),
  notIn: z.lazy(() => NodeTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => NestedEnumNodeTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumNodeTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumNodeTypeFilterSchema).optional()
}).strict();

export const NestedEnumSourceTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumSourceTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SourceTypeSchema).optional(),
  in: z.lazy(() => SourceTypeSchema).array().optional(),
  notIn: z.lazy(() => SourceTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => NestedEnumSourceTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSourceTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSourceTypeFilterSchema).optional()
}).strict();

export const NestedBoolWithAggregatesFilterSchema: z.ZodType<Prisma.NestedBoolWithAggregatesFilter> = z.object({
  equals: z.boolean().optional(),
  not: z.union([ z.boolean(),z.lazy(() => NestedBoolWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedBoolFilterSchema).optional(),
  _max: z.lazy(() => NestedBoolFilterSchema).optional()
}).strict();

export const NestedEnumRelationshipTypeFilterSchema: z.ZodType<Prisma.NestedEnumRelationshipTypeFilter> = z.object({
  equals: z.lazy(() => RelationshipTypeSchema).optional(),
  in: z.lazy(() => RelationshipTypeSchema).array().optional(),
  notIn: z.lazy(() => RelationshipTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => NestedEnumRelationshipTypeFilterSchema) ]).optional(),
}).strict();

export const NestedEnumRelationshipTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumRelationshipTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => RelationshipTypeSchema).optional(),
  in: z.lazy(() => RelationshipTypeSchema).array().optional(),
  notIn: z.lazy(() => RelationshipTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => NestedEnumRelationshipTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumRelationshipTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumRelationshipTypeFilterSchema).optional()
}).strict();

export const NestedEnumPersonaTypeFilterSchema: z.ZodType<Prisma.NestedEnumPersonaTypeFilter> = z.object({
  equals: z.lazy(() => PersonaTypeSchema).optional(),
  in: z.lazy(() => PersonaTypeSchema).array().optional(),
  notIn: z.lazy(() => PersonaTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => NestedEnumPersonaTypeFilterSchema) ]).optional(),
}).strict();

export const NestedEnumPersonaTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumPersonaTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => PersonaTypeSchema).optional(),
  in: z.lazy(() => PersonaTypeSchema).array().optional(),
  notIn: z.lazy(() => PersonaTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => NestedEnumPersonaTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumPersonaTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumPersonaTypeFilterSchema).optional()
}).strict();

export const NestedEnumSessionStatusFilterSchema: z.ZodType<Prisma.NestedEnumSessionStatusFilter> = z.object({
  equals: z.lazy(() => SessionStatusSchema).optional(),
  in: z.lazy(() => SessionStatusSchema).array().optional(),
  notIn: z.lazy(() => SessionStatusSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => NestedEnumSessionStatusFilterSchema) ]).optional(),
}).strict();

export const NestedEnumSessionTypeFilterSchema: z.ZodType<Prisma.NestedEnumSessionTypeFilter> = z.object({
  equals: z.lazy(() => SessionTypeSchema).optional(),
  in: z.lazy(() => SessionTypeSchema).array().optional(),
  notIn: z.lazy(() => SessionTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => NestedEnumSessionTypeFilterSchema) ]).optional(),
}).strict();

export const NestedEnumSessionStatusWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumSessionStatusWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SessionStatusSchema).optional(),
  in: z.lazy(() => SessionStatusSchema).array().optional(),
  notIn: z.lazy(() => SessionStatusSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => NestedEnumSessionStatusWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSessionStatusFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSessionStatusFilterSchema).optional()
}).strict();

export const NestedEnumSessionTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumSessionTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SessionTypeSchema).optional(),
  in: z.lazy(() => SessionTypeSchema).array().optional(),
  notIn: z.lazy(() => SessionTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => NestedEnumSessionTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSessionTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSessionTypeFilterSchema).optional()
}).strict();

export const NestedEnumRoleTypeFilterSchema: z.ZodType<Prisma.NestedEnumRoleTypeFilter> = z.object({
  equals: z.lazy(() => RoleTypeSchema).optional(),
  in: z.lazy(() => RoleTypeSchema).array().optional(),
  notIn: z.lazy(() => RoleTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => NestedEnumRoleTypeFilterSchema) ]).optional(),
}).strict();

export const NestedEnumRoleTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumRoleTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => RoleTypeSchema).optional(),
  in: z.lazy(() => RoleTypeSchema).array().optional(),
  notIn: z.lazy(() => RoleTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => NestedEnumRoleTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumRoleTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumRoleTypeFilterSchema).optional()
}).strict();

export const NestedEnumSessionRoleFilterSchema: z.ZodType<Prisma.NestedEnumSessionRoleFilter> = z.object({
  equals: z.lazy(() => SessionRoleSchema).optional(),
  in: z.lazy(() => SessionRoleSchema).array().optional(),
  notIn: z.lazy(() => SessionRoleSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => NestedEnumSessionRoleFilterSchema) ]).optional(),
}).strict();

export const NestedEnumSessionRoleWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumSessionRoleWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SessionRoleSchema).optional(),
  in: z.lazy(() => SessionRoleSchema).array().optional(),
  notIn: z.lazy(() => SessionRoleSchema).array().optional(),
  not: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => NestedEnumSessionRoleWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSessionRoleFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSessionRoleFilterSchema).optional()
}).strict();

export const KnowledgeNodeCreateWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutAuthorInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutAuthorInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutAuthorInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema) ]),
}).strict();

export const KnowledgeNodeCreateManyAuthorInputEnvelopeSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyAuthorInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => KnowledgeNodeCreateManyAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateManyAuthorInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const ValidationCreateWithoutExpertInputSchema: z.ZodType<Prisma.ValidationCreateWithoutExpertInput> = z.object({
  id: z.string().cuid().optional(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  node: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutValidationsInputSchema)
}).strict();

export const ValidationUncheckedCreateWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateWithoutExpertInput> = z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationCreateOrConnectWithoutExpertInputSchema: z.ZodType<Prisma.ValidationCreateOrConnectWithoutExpertInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema) ]),
}).strict();

export const ValidationCreateManyExpertInputEnvelopeSchema: z.ZodType<Prisma.ValidationCreateManyExpertInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => ValidationCreateManyExpertInputSchema),z.lazy(() => ValidationCreateManyExpertInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutAuthorInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutAuthorInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyWithWhereWithoutAuthorInput> = z.object({
  where: z.lazy(() => KnowledgeNodeScalarWhereInputSchema),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateManyMutationInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutAuthorInputSchema) ]),
}).strict();

export const KnowledgeNodeScalarWhereInputSchema: z.ZodType<Prisma.KnowledgeNodeScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumNodeTypeFilterSchema),z.lazy(() => NodeTypeSchema) ]).optional(),
  source: z.union([ z.lazy(() => EnumSourceTypeFilterSchema),z.lazy(() => SourceTypeSchema) ]).optional(),
  context: z.lazy(() => JsonNullableFilterSchema).optional(),
  validated: z.union([ z.lazy(() => BoolFilterSchema),z.boolean() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  authorId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
}).strict();

export const ValidationUpsertWithWhereUniqueWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUpsertWithWhereUniqueWithoutExpertInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => ValidationUpdateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedUpdateWithoutExpertInputSchema) ]),
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema) ]),
}).strict();

export const ValidationUpdateWithWhereUniqueWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUpdateWithWhereUniqueWithoutExpertInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => ValidationUpdateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedUpdateWithoutExpertInputSchema) ]),
}).strict();

export const ValidationUpdateManyWithWhereWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUpdateManyWithWhereWithoutExpertInput> = z.object({
  where: z.lazy(() => ValidationScalarWhereInputSchema),
  data: z.union([ z.lazy(() => ValidationUpdateManyMutationInputSchema),z.lazy(() => ValidationUncheckedUpdateManyWithoutExpertInputSchema) ]),
}).strict();

export const ValidationScalarWhereInputSchema: z.ZodType<Prisma.ValidationScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => ValidationScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  expertId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  notes: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const UserCreateWithoutNodesInputSchema: z.ZodType<Prisma.UserCreateWithoutNodesInput> = z.object({
  userId: z.string(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutExpertInputSchema).optional()
}).strict();

export const UserUncheckedCreateWithoutNodesInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutNodesInput> = z.object({
  userId: z.string(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutExpertInputSchema).optional()
}).strict();

export const UserCreateOrConnectWithoutNodesInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutNodesInput> = z.object({
  where: z.lazy(() => UserWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => UserCreateWithoutNodesInputSchema),z.lazy(() => UserUncheckedCreateWithoutNodesInputSchema) ]),
}).strict();

export const NodeRelationshipCreateWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipCreateWithoutFromInput> = z.object({
  id: z.string().cuid().optional(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  to: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutRelationshipsToInputSchema)
}).strict();

export const NodeRelationshipUncheckedCreateWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateWithoutFromInput> = z.object({
  id: z.string().cuid().optional(),
  toId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipCreateOrConnectWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipCreateOrConnectWithoutFromInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema) ]),
}).strict();

export const NodeRelationshipCreateManyFromInputEnvelopeSchema: z.ZodType<Prisma.NodeRelationshipCreateManyFromInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => NodeRelationshipCreateManyFromInputSchema),z.lazy(() => NodeRelationshipCreateManyFromInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const NodeRelationshipCreateWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipCreateWithoutToInput> = z.object({
  id: z.string().cuid().optional(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  from: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutRelationshipsFromInputSchema)
}).strict();

export const NodeRelationshipUncheckedCreateWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateWithoutToInput> = z.object({
  id: z.string().cuid().optional(),
  fromId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipCreateOrConnectWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipCreateOrConnectWithoutToInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema) ]),
}).strict();

export const NodeRelationshipCreateManyToInputEnvelopeSchema: z.ZodType<Prisma.NodeRelationshipCreateManyToInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => NodeRelationshipCreateManyToInputSchema),z.lazy(() => NodeRelationshipCreateManyToInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const ValidationCreateWithoutNodeInputSchema: z.ZodType<Prisma.ValidationCreateWithoutNodeInput> = z.object({
  id: z.string().cuid().optional(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  expert: z.lazy(() => UserCreateNestedOneWithoutValidationsInputSchema)
}).strict();

export const ValidationUncheckedCreateWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateWithoutNodeInput> = z.object({
  id: z.string().cuid().optional(),
  expertId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationCreateOrConnectWithoutNodeInputSchema: z.ZodType<Prisma.ValidationCreateOrConnectWithoutNodeInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema) ]),
}).strict();

export const ValidationCreateManyNodeInputEnvelopeSchema: z.ZodType<Prisma.ValidationCreateManyNodeInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => ValidationCreateManyNodeInputSchema),z.lazy(() => ValidationCreateManyNodeInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const TopicCreateWithoutNodesInputSchema: z.ZodType<Prisma.TopicCreateWithoutNodesInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const TopicUncheckedCreateWithoutNodesInputSchema: z.ZodType<Prisma.TopicUncheckedCreateWithoutNodesInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const TopicCreateOrConnectWithoutNodesInputSchema: z.ZodType<Prisma.TopicCreateOrConnectWithoutNodesInput> = z.object({
  where: z.lazy(() => TopicWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema) ]),
}).strict();

export const UserUpsertWithoutNodesInputSchema: z.ZodType<Prisma.UserUpsertWithoutNodesInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutNodesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNodesInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutNodesInputSchema),z.lazy(() => UserUncheckedCreateWithoutNodesInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export const UserUpdateToOneWithWhereWithoutNodesInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutNodesInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutNodesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNodesInputSchema) ]),
}).strict();

export const UserUpdateWithoutNodesInputSchema: z.ZodType<Prisma.UserUpdateWithoutNodesInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutExpertNestedInputSchema).optional()
}).strict();

export const UserUncheckedUpdateWithoutNodesInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutNodesInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutExpertNestedInputSchema).optional()
}).strict();

export const NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUpsertWithWhereUniqueWithoutFromInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateWithoutFromInputSchema) ]),
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema) ]),
}).strict();

export const NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateWithWhereUniqueWithoutFromInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => NodeRelationshipUpdateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateWithoutFromInputSchema) ]),
}).strict();

export const NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyWithWhereWithoutFromInput> = z.object({
  where: z.lazy(() => NodeRelationshipScalarWhereInputSchema),
  data: z.union([ z.lazy(() => NodeRelationshipUpdateManyMutationInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromInputSchema) ]),
}).strict();

export const NodeRelationshipScalarWhereInputSchema: z.ZodType<Prisma.NodeRelationshipScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => NodeRelationshipScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  fromId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  toId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumRelationshipTypeFilterSchema),z.lazy(() => RelationshipTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUpsertWithWhereUniqueWithoutToInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateWithoutToInputSchema) ]),
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema) ]),
}).strict();

export const NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateWithWhereUniqueWithoutToInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => NodeRelationshipUpdateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateWithoutToInputSchema) ]),
}).strict();

export const NodeRelationshipUpdateManyWithWhereWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyWithWhereWithoutToInput> = z.object({
  where: z.lazy(() => NodeRelationshipScalarWhereInputSchema),
  data: z.union([ z.lazy(() => NodeRelationshipUpdateManyMutationInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToInputSchema) ]),
}).strict();

export const ValidationUpsertWithWhereUniqueWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUpsertWithWhereUniqueWithoutNodeInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => ValidationUpdateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedUpdateWithoutNodeInputSchema) ]),
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema) ]),
}).strict();

export const ValidationUpdateWithWhereUniqueWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUpdateWithWhereUniqueWithoutNodeInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => ValidationUpdateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedUpdateWithoutNodeInputSchema) ]),
}).strict();

export const ValidationUpdateManyWithWhereWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUpdateManyWithWhereWithoutNodeInput> = z.object({
  where: z.lazy(() => ValidationScalarWhereInputSchema),
  data: z.union([ z.lazy(() => ValidationUpdateManyMutationInputSchema),z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeInputSchema) ]),
}).strict();

export const TopicUpsertWithWhereUniqueWithoutNodesInputSchema: z.ZodType<Prisma.TopicUpsertWithWhereUniqueWithoutNodesInput> = z.object({
  where: z.lazy(() => TopicWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => TopicUpdateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedUpdateWithoutNodesInputSchema) ]),
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema) ]),
}).strict();

export const TopicUpdateWithWhereUniqueWithoutNodesInputSchema: z.ZodType<Prisma.TopicUpdateWithWhereUniqueWithoutNodesInput> = z.object({
  where: z.lazy(() => TopicWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => TopicUpdateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedUpdateWithoutNodesInputSchema) ]),
}).strict();

export const TopicUpdateManyWithWhereWithoutNodesInputSchema: z.ZodType<Prisma.TopicUpdateManyWithWhereWithoutNodesInput> = z.object({
  where: z.lazy(() => TopicScalarWhereInputSchema),
  data: z.union([ z.lazy(() => TopicUpdateManyMutationInputSchema),z.lazy(() => TopicUncheckedUpdateManyWithoutNodesInputSchema) ]),
}).strict();

export const TopicScalarWhereInputSchema: z.ZodType<Prisma.TopicScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => TopicScalarWhereInputSchema),z.lazy(() => TopicScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TopicScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TopicScalarWhereInputSchema),z.lazy(() => TopicScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  name: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const KnowledgeNodeCreateWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutRelationshipsFromInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutRelationshipsFromInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema) ]),
}).strict();

export const KnowledgeNodeCreateWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutRelationshipsToInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutRelationshipsToInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutRelationshipsToInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema) ]),
}).strict();

export const KnowledgeNodeUpsertWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithoutRelationshipsFromInput> = z.object({
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema) ]),
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsFromInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutRelationshipsFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUpsertWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithoutRelationshipsToInput> = z.object({
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema) ]),
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsToInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutRelationshipsToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutValidationsInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutValidationsInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutValidationsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema) ]),
}).strict();

export const UserCreateWithoutValidationsInputSchema: z.ZodType<Prisma.UserCreateWithoutValidationsInput> = z.object({
  userId: z.string(),
  nodes: z.lazy(() => KnowledgeNodeCreateNestedManyWithoutAuthorInputSchema).optional()
}).strict();

export const UserUncheckedCreateWithoutValidationsInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutValidationsInput> = z.object({
  userId: z.string(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedCreateNestedManyWithoutAuthorInputSchema).optional()
}).strict();

export const UserCreateOrConnectWithoutValidationsInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutValidationsInput> = z.object({
  where: z.lazy(() => UserWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => UserCreateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutValidationsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpsertWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithoutValidationsInput> = z.object({
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutValidationsInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema) ]),
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateToOneWithWhereWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateToOneWithWhereWithoutValidationsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutValidationsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutValidationsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutValidationsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const UserUpsertWithoutValidationsInputSchema: z.ZodType<Prisma.UserUpsertWithoutValidationsInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutValidationsInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutValidationsInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export const UserUpdateToOneWithWhereWithoutValidationsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutValidationsInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutValidationsInputSchema) ]),
}).strict();

export const UserUpdateWithoutValidationsInputSchema: z.ZodType<Prisma.UserUpdateWithoutValidationsInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUpdateManyWithoutAuthorNestedInputSchema).optional()
}).strict();

export const UserUncheckedUpdateWithoutValidationsInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutValidationsInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutAuthorNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutTopicsInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutTopicsInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutTopicsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutTopicsInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutTopicsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyWithWhereWithoutTopicsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeScalarWhereInputSchema),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateManyMutationInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutTopicsInputSchema) ]),
}).strict();

export const QnAPairCreateWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairCreateWithoutAgentInput> = z.object({
  id: z.string().cuid().optional(),
  question: z.string(),
  answer: z.string(),
  persona: z.lazy(() => PersonaTypeSchema),
  createdAt: z.coerce.date().optional()
}).strict();

export const QnAPairUncheckedCreateWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairUncheckedCreateWithoutAgentInput> = z.object({
  id: z.string().cuid().optional(),
  question: z.string(),
  answer: z.string(),
  persona: z.lazy(() => PersonaTypeSchema),
  createdAt: z.coerce.date().optional()
}).strict();

export const QnAPairCreateOrConnectWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairCreateOrConnectWithoutAgentInput> = z.object({
  where: z.lazy(() => QnAPairWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => QnAPairCreateWithoutAgentInputSchema),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema) ]),
}).strict();

export const QnAPairCreateManyAgentInputEnvelopeSchema: z.ZodType<Prisma.QnAPairCreateManyAgentInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => QnAPairCreateManyAgentInputSchema),z.lazy(() => QnAPairCreateManyAgentInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const SessionCreateWithoutAgentInputSchema: z.ZodType<Prisma.SessionCreateWithoutAgentInput> = z.object({
  id: z.string().cuid().optional(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional(),
  messages: z.lazy(() => SessionMessageCreateNestedManyWithoutSessionInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantCreateNestedManyWithoutSessionInputSchema).optional()
}).strict();

export const SessionUncheckedCreateWithoutAgentInputSchema: z.ZodType<Prisma.SessionUncheckedCreateWithoutAgentInput> = z.object({
  id: z.string().cuid().optional(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional(),
  messages: z.lazy(() => SessionMessageUncheckedCreateNestedManyWithoutSessionInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantUncheckedCreateNestedManyWithoutSessionInputSchema).optional()
}).strict();

export const SessionCreateOrConnectWithoutAgentInputSchema: z.ZodType<Prisma.SessionCreateOrConnectWithoutAgentInput> = z.object({
  where: z.lazy(() => SessionWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => SessionCreateWithoutAgentInputSchema),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema) ]),
}).strict();

export const SessionCreateManyAgentInputEnvelopeSchema: z.ZodType<Prisma.SessionCreateManyAgentInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => SessionCreateManyAgentInputSchema),z.lazy(() => SessionCreateManyAgentInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const QnAPairUpsertWithWhereUniqueWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairUpsertWithWhereUniqueWithoutAgentInput> = z.object({
  where: z.lazy(() => QnAPairWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => QnAPairUpdateWithoutAgentInputSchema),z.lazy(() => QnAPairUncheckedUpdateWithoutAgentInputSchema) ]),
  create: z.union([ z.lazy(() => QnAPairCreateWithoutAgentInputSchema),z.lazy(() => QnAPairUncheckedCreateWithoutAgentInputSchema) ]),
}).strict();

export const QnAPairUpdateWithWhereUniqueWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairUpdateWithWhereUniqueWithoutAgentInput> = z.object({
  where: z.lazy(() => QnAPairWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => QnAPairUpdateWithoutAgentInputSchema),z.lazy(() => QnAPairUncheckedUpdateWithoutAgentInputSchema) ]),
}).strict();

export const QnAPairUpdateManyWithWhereWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairUpdateManyWithWhereWithoutAgentInput> = z.object({
  where: z.lazy(() => QnAPairScalarWhereInputSchema),
  data: z.union([ z.lazy(() => QnAPairUpdateManyMutationInputSchema),z.lazy(() => QnAPairUncheckedUpdateManyWithoutAgentInputSchema) ]),
}).strict();

export const QnAPairScalarWhereInputSchema: z.ZodType<Prisma.QnAPairScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => QnAPairScalarWhereInputSchema),z.lazy(() => QnAPairScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => QnAPairScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => QnAPairScalarWhereInputSchema),z.lazy(() => QnAPairScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  agentId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  question: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  answer: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  persona: z.union([ z.lazy(() => EnumPersonaTypeFilterSchema),z.lazy(() => PersonaTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const SessionUpsertWithWhereUniqueWithoutAgentInputSchema: z.ZodType<Prisma.SessionUpsertWithWhereUniqueWithoutAgentInput> = z.object({
  where: z.lazy(() => SessionWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => SessionUpdateWithoutAgentInputSchema),z.lazy(() => SessionUncheckedUpdateWithoutAgentInputSchema) ]),
  create: z.union([ z.lazy(() => SessionCreateWithoutAgentInputSchema),z.lazy(() => SessionUncheckedCreateWithoutAgentInputSchema) ]),
}).strict();

export const SessionUpdateWithWhereUniqueWithoutAgentInputSchema: z.ZodType<Prisma.SessionUpdateWithWhereUniqueWithoutAgentInput> = z.object({
  where: z.lazy(() => SessionWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => SessionUpdateWithoutAgentInputSchema),z.lazy(() => SessionUncheckedUpdateWithoutAgentInputSchema) ]),
}).strict();

export const SessionUpdateManyWithWhereWithoutAgentInputSchema: z.ZodType<Prisma.SessionUpdateManyWithWhereWithoutAgentInput> = z.object({
  where: z.lazy(() => SessionScalarWhereInputSchema),
  data: z.union([ z.lazy(() => SessionUpdateManyMutationInputSchema),z.lazy(() => SessionUncheckedUpdateManyWithoutAgentInputSchema) ]),
}).strict();

export const SessionScalarWhereInputSchema: z.ZodType<Prisma.SessionScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => SessionScalarWhereInputSchema),z.lazy(() => SessionScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionScalarWhereInputSchema),z.lazy(() => SessionScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  agentId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  status: z.union([ z.lazy(() => EnumSessionStatusFilterSchema),z.lazy(() => SessionStatusSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  type: z.union([ z.lazy(() => EnumSessionTypeFilterSchema),z.lazy(() => SessionTypeSchema) ]).optional(),
}).strict();

export const AgentCreateWithoutQnaPairsInputSchema: z.ZodType<Prisma.AgentCreateWithoutQnaPairsInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  description: z.string().optional().nullable(),
  povSummary: z.string().optional().nullable(),
  ownerId: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  sessions: z.lazy(() => SessionCreateNestedManyWithoutAgentInputSchema).optional()
}).strict();

export const AgentUncheckedCreateWithoutQnaPairsInputSchema: z.ZodType<Prisma.AgentUncheckedCreateWithoutQnaPairsInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  description: z.string().optional().nullable(),
  povSummary: z.string().optional().nullable(),
  ownerId: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  sessions: z.lazy(() => SessionUncheckedCreateNestedManyWithoutAgentInputSchema).optional()
}).strict();

export const AgentCreateOrConnectWithoutQnaPairsInputSchema: z.ZodType<Prisma.AgentCreateOrConnectWithoutQnaPairsInput> = z.object({
  where: z.lazy(() => AgentWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => AgentCreateWithoutQnaPairsInputSchema),z.lazy(() => AgentUncheckedCreateWithoutQnaPairsInputSchema) ]),
}).strict();

export const AgentUpsertWithoutQnaPairsInputSchema: z.ZodType<Prisma.AgentUpsertWithoutQnaPairsInput> = z.object({
  update: z.union([ z.lazy(() => AgentUpdateWithoutQnaPairsInputSchema),z.lazy(() => AgentUncheckedUpdateWithoutQnaPairsInputSchema) ]),
  create: z.union([ z.lazy(() => AgentCreateWithoutQnaPairsInputSchema),z.lazy(() => AgentUncheckedCreateWithoutQnaPairsInputSchema) ]),
  where: z.lazy(() => AgentWhereInputSchema).optional()
}).strict();

export const AgentUpdateToOneWithWhereWithoutQnaPairsInputSchema: z.ZodType<Prisma.AgentUpdateToOneWithWhereWithoutQnaPairsInput> = z.object({
  where: z.lazy(() => AgentWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => AgentUpdateWithoutQnaPairsInputSchema),z.lazy(() => AgentUncheckedUpdateWithoutQnaPairsInputSchema) ]),
}).strict();

export const AgentUpdateWithoutQnaPairsInputSchema: z.ZodType<Prisma.AgentUpdateWithoutQnaPairsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  description: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  povSummary: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  ownerId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  sessions: z.lazy(() => SessionUpdateManyWithoutAgentNestedInputSchema).optional()
}).strict();

export const AgentUncheckedUpdateWithoutQnaPairsInputSchema: z.ZodType<Prisma.AgentUncheckedUpdateWithoutQnaPairsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  description: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  povSummary: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  ownerId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  sessions: z.lazy(() => SessionUncheckedUpdateManyWithoutAgentNestedInputSchema).optional()
}).strict();

export const AgentCreateWithoutSessionsInputSchema: z.ZodType<Prisma.AgentCreateWithoutSessionsInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  description: z.string().optional().nullable(),
  povSummary: z.string().optional().nullable(),
  ownerId: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  qnaPairs: z.lazy(() => QnAPairCreateNestedManyWithoutAgentInputSchema).optional()
}).strict();

export const AgentUncheckedCreateWithoutSessionsInputSchema: z.ZodType<Prisma.AgentUncheckedCreateWithoutSessionsInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  description: z.string().optional().nullable(),
  povSummary: z.string().optional().nullable(),
  ownerId: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  qnaPairs: z.lazy(() => QnAPairUncheckedCreateNestedManyWithoutAgentInputSchema).optional()
}).strict();

export const AgentCreateOrConnectWithoutSessionsInputSchema: z.ZodType<Prisma.AgentCreateOrConnectWithoutSessionsInput> = z.object({
  where: z.lazy(() => AgentWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => AgentCreateWithoutSessionsInputSchema),z.lazy(() => AgentUncheckedCreateWithoutSessionsInputSchema) ]),
}).strict();

export const SessionMessageCreateWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageCreateWithoutSessionInput> = z.object({
  id: z.string().cuid().optional(),
  senderId: z.string().optional().nullable(),
  role: z.lazy(() => RoleTypeSchema),
  content: z.string(),
  createdAt: z.coerce.date().optional()
}).strict();

export const SessionMessageUncheckedCreateWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageUncheckedCreateWithoutSessionInput> = z.object({
  id: z.string().cuid().optional(),
  senderId: z.string().optional().nullable(),
  role: z.lazy(() => RoleTypeSchema),
  content: z.string(),
  createdAt: z.coerce.date().optional()
}).strict();

export const SessionMessageCreateOrConnectWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageCreateOrConnectWithoutSessionInput> = z.object({
  where: z.lazy(() => SessionMessageWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => SessionMessageCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema) ]),
}).strict();

export const SessionMessageCreateManySessionInputEnvelopeSchema: z.ZodType<Prisma.SessionMessageCreateManySessionInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => SessionMessageCreateManySessionInputSchema),z.lazy(() => SessionMessageCreateManySessionInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const SessionParticipantCreateWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantCreateWithoutSessionInput> = z.object({
  id: z.string().cuid().optional(),
  userId: z.string(),
  role: z.lazy(() => SessionRoleSchema),
  joinedAt: z.coerce.date().optional()
}).strict();

export const SessionParticipantUncheckedCreateWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantUncheckedCreateWithoutSessionInput> = z.object({
  id: z.string().cuid().optional(),
  userId: z.string(),
  role: z.lazy(() => SessionRoleSchema),
  joinedAt: z.coerce.date().optional()
}).strict();

export const SessionParticipantCreateOrConnectWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantCreateOrConnectWithoutSessionInput> = z.object({
  where: z.lazy(() => SessionParticipantWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema) ]),
}).strict();

export const SessionParticipantCreateManySessionInputEnvelopeSchema: z.ZodType<Prisma.SessionParticipantCreateManySessionInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => SessionParticipantCreateManySessionInputSchema),z.lazy(() => SessionParticipantCreateManySessionInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const AgentUpsertWithoutSessionsInputSchema: z.ZodType<Prisma.AgentUpsertWithoutSessionsInput> = z.object({
  update: z.union([ z.lazy(() => AgentUpdateWithoutSessionsInputSchema),z.lazy(() => AgentUncheckedUpdateWithoutSessionsInputSchema) ]),
  create: z.union([ z.lazy(() => AgentCreateWithoutSessionsInputSchema),z.lazy(() => AgentUncheckedCreateWithoutSessionsInputSchema) ]),
  where: z.lazy(() => AgentWhereInputSchema).optional()
}).strict();

export const AgentUpdateToOneWithWhereWithoutSessionsInputSchema: z.ZodType<Prisma.AgentUpdateToOneWithWhereWithoutSessionsInput> = z.object({
  where: z.lazy(() => AgentWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => AgentUpdateWithoutSessionsInputSchema),z.lazy(() => AgentUncheckedUpdateWithoutSessionsInputSchema) ]),
}).strict();

export const AgentUpdateWithoutSessionsInputSchema: z.ZodType<Prisma.AgentUpdateWithoutSessionsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  description: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  povSummary: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  ownerId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  qnaPairs: z.lazy(() => QnAPairUpdateManyWithoutAgentNestedInputSchema).optional()
}).strict();

export const AgentUncheckedUpdateWithoutSessionsInputSchema: z.ZodType<Prisma.AgentUncheckedUpdateWithoutSessionsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  description: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  povSummary: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  ownerId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  qnaPairs: z.lazy(() => QnAPairUncheckedUpdateManyWithoutAgentNestedInputSchema).optional()
}).strict();

export const SessionMessageUpsertWithWhereUniqueWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageUpsertWithWhereUniqueWithoutSessionInput> = z.object({
  where: z.lazy(() => SessionMessageWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => SessionMessageUpdateWithoutSessionInputSchema),z.lazy(() => SessionMessageUncheckedUpdateWithoutSessionInputSchema) ]),
  create: z.union([ z.lazy(() => SessionMessageCreateWithoutSessionInputSchema),z.lazy(() => SessionMessageUncheckedCreateWithoutSessionInputSchema) ]),
}).strict();

export const SessionMessageUpdateWithWhereUniqueWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageUpdateWithWhereUniqueWithoutSessionInput> = z.object({
  where: z.lazy(() => SessionMessageWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => SessionMessageUpdateWithoutSessionInputSchema),z.lazy(() => SessionMessageUncheckedUpdateWithoutSessionInputSchema) ]),
}).strict();

export const SessionMessageUpdateManyWithWhereWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageUpdateManyWithWhereWithoutSessionInput> = z.object({
  where: z.lazy(() => SessionMessageScalarWhereInputSchema),
  data: z.union([ z.lazy(() => SessionMessageUpdateManyMutationInputSchema),z.lazy(() => SessionMessageUncheckedUpdateManyWithoutSessionInputSchema) ]),
}).strict();

export const SessionMessageScalarWhereInputSchema: z.ZodType<Prisma.SessionMessageScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => SessionMessageScalarWhereInputSchema),z.lazy(() => SessionMessageScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionMessageScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionMessageScalarWhereInputSchema),z.lazy(() => SessionMessageScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  sessionId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  senderId: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  role: z.union([ z.lazy(() => EnumRoleTypeFilterSchema),z.lazy(() => RoleTypeSchema) ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const SessionParticipantUpsertWithWhereUniqueWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantUpsertWithWhereUniqueWithoutSessionInput> = z.object({
  where: z.lazy(() => SessionParticipantWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => SessionParticipantUpdateWithoutSessionInputSchema),z.lazy(() => SessionParticipantUncheckedUpdateWithoutSessionInputSchema) ]),
  create: z.union([ z.lazy(() => SessionParticipantCreateWithoutSessionInputSchema),z.lazy(() => SessionParticipantUncheckedCreateWithoutSessionInputSchema) ]),
}).strict();

export const SessionParticipantUpdateWithWhereUniqueWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantUpdateWithWhereUniqueWithoutSessionInput> = z.object({
  where: z.lazy(() => SessionParticipantWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => SessionParticipantUpdateWithoutSessionInputSchema),z.lazy(() => SessionParticipantUncheckedUpdateWithoutSessionInputSchema) ]),
}).strict();

export const SessionParticipantUpdateManyWithWhereWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantUpdateManyWithWhereWithoutSessionInput> = z.object({
  where: z.lazy(() => SessionParticipantScalarWhereInputSchema),
  data: z.union([ z.lazy(() => SessionParticipantUpdateManyMutationInputSchema),z.lazy(() => SessionParticipantUncheckedUpdateManyWithoutSessionInputSchema) ]),
}).strict();

export const SessionParticipantScalarWhereInputSchema: z.ZodType<Prisma.SessionParticipantScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => SessionParticipantScalarWhereInputSchema),z.lazy(() => SessionParticipantScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => SessionParticipantScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => SessionParticipantScalarWhereInputSchema),z.lazy(() => SessionParticipantScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  sessionId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  userId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  role: z.union([ z.lazy(() => EnumSessionRoleFilterSchema),z.lazy(() => SessionRoleSchema) ]).optional(),
  joinedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const SessionCreateWithoutMessagesInputSchema: z.ZodType<Prisma.SessionCreateWithoutMessagesInput> = z.object({
  id: z.string().cuid().optional(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional(),
  agent: z.lazy(() => AgentCreateNestedOneWithoutSessionsInputSchema),
  participants: z.lazy(() => SessionParticipantCreateNestedManyWithoutSessionInputSchema).optional()
}).strict();

export const SessionUncheckedCreateWithoutMessagesInputSchema: z.ZodType<Prisma.SessionUncheckedCreateWithoutMessagesInput> = z.object({
  id: z.string().cuid().optional(),
  agentId: z.string(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional(),
  participants: z.lazy(() => SessionParticipantUncheckedCreateNestedManyWithoutSessionInputSchema).optional()
}).strict();

export const SessionCreateOrConnectWithoutMessagesInputSchema: z.ZodType<Prisma.SessionCreateOrConnectWithoutMessagesInput> = z.object({
  where: z.lazy(() => SessionWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => SessionCreateWithoutMessagesInputSchema),z.lazy(() => SessionUncheckedCreateWithoutMessagesInputSchema) ]),
}).strict();

export const SessionUpsertWithoutMessagesInputSchema: z.ZodType<Prisma.SessionUpsertWithoutMessagesInput> = z.object({
  update: z.union([ z.lazy(() => SessionUpdateWithoutMessagesInputSchema),z.lazy(() => SessionUncheckedUpdateWithoutMessagesInputSchema) ]),
  create: z.union([ z.lazy(() => SessionCreateWithoutMessagesInputSchema),z.lazy(() => SessionUncheckedCreateWithoutMessagesInputSchema) ]),
  where: z.lazy(() => SessionWhereInputSchema).optional()
}).strict();

export const SessionUpdateToOneWithWhereWithoutMessagesInputSchema: z.ZodType<Prisma.SessionUpdateToOneWithWhereWithoutMessagesInput> = z.object({
  where: z.lazy(() => SessionWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => SessionUpdateWithoutMessagesInputSchema),z.lazy(() => SessionUncheckedUpdateWithoutMessagesInputSchema) ]),
}).strict();

export const SessionUpdateWithoutMessagesInputSchema: z.ZodType<Prisma.SessionUpdateWithoutMessagesInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
  agent: z.lazy(() => AgentUpdateOneRequiredWithoutSessionsNestedInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantUpdateManyWithoutSessionNestedInputSchema).optional()
}).strict();

export const SessionUncheckedUpdateWithoutMessagesInputSchema: z.ZodType<Prisma.SessionUncheckedUpdateWithoutMessagesInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  agentId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
  participants: z.lazy(() => SessionParticipantUncheckedUpdateManyWithoutSessionNestedInputSchema).optional()
}).strict();

export const SessionCreateWithoutParticipantsInputSchema: z.ZodType<Prisma.SessionCreateWithoutParticipantsInput> = z.object({
  id: z.string().cuid().optional(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional(),
  agent: z.lazy(() => AgentCreateNestedOneWithoutSessionsInputSchema),
  messages: z.lazy(() => SessionMessageCreateNestedManyWithoutSessionInputSchema).optional()
}).strict();

export const SessionUncheckedCreateWithoutParticipantsInputSchema: z.ZodType<Prisma.SessionUncheckedCreateWithoutParticipantsInput> = z.object({
  id: z.string().cuid().optional(),
  agentId: z.string(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional(),
  messages: z.lazy(() => SessionMessageUncheckedCreateNestedManyWithoutSessionInputSchema).optional()
}).strict();

export const SessionCreateOrConnectWithoutParticipantsInputSchema: z.ZodType<Prisma.SessionCreateOrConnectWithoutParticipantsInput> = z.object({
  where: z.lazy(() => SessionWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => SessionCreateWithoutParticipantsInputSchema),z.lazy(() => SessionUncheckedCreateWithoutParticipantsInputSchema) ]),
}).strict();

export const SessionUpsertWithoutParticipantsInputSchema: z.ZodType<Prisma.SessionUpsertWithoutParticipantsInput> = z.object({
  update: z.union([ z.lazy(() => SessionUpdateWithoutParticipantsInputSchema),z.lazy(() => SessionUncheckedUpdateWithoutParticipantsInputSchema) ]),
  create: z.union([ z.lazy(() => SessionCreateWithoutParticipantsInputSchema),z.lazy(() => SessionUncheckedCreateWithoutParticipantsInputSchema) ]),
  where: z.lazy(() => SessionWhereInputSchema).optional()
}).strict();

export const SessionUpdateToOneWithWhereWithoutParticipantsInputSchema: z.ZodType<Prisma.SessionUpdateToOneWithWhereWithoutParticipantsInput> = z.object({
  where: z.lazy(() => SessionWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => SessionUpdateWithoutParticipantsInputSchema),z.lazy(() => SessionUncheckedUpdateWithoutParticipantsInputSchema) ]),
}).strict();

export const SessionUpdateWithoutParticipantsInputSchema: z.ZodType<Prisma.SessionUpdateWithoutParticipantsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
  agent: z.lazy(() => AgentUpdateOneRequiredWithoutSessionsNestedInputSchema).optional(),
  messages: z.lazy(() => SessionMessageUpdateManyWithoutSessionNestedInputSchema).optional()
}).strict();

export const SessionUncheckedUpdateWithoutParticipantsInputSchema: z.ZodType<Prisma.SessionUncheckedUpdateWithoutParticipantsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  agentId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
  messages: z.lazy(() => SessionMessageUncheckedUpdateManyWithoutSessionNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateManyAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyAuthorInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationCreateManyExpertInputSchema: z.ZodType<Prisma.ValidationCreateManyExpertInput> = z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const KnowledgeNodeUpdateWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutAuthorInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutAuthorInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateManyWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyWithoutAuthorInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUpdateWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUpdateWithoutExpertInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  node: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutValidationsNestedInputSchema).optional()
}).strict();

export const ValidationUncheckedUpdateWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateWithoutExpertInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyWithoutExpertInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipCreateManyFromInputSchema: z.ZodType<Prisma.NodeRelationshipCreateManyFromInput> = z.object({
  id: z.string().cuid().optional(),
  toId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipCreateManyToInputSchema: z.ZodType<Prisma.NodeRelationshipCreateManyToInput> = z.object({
  id: z.string().cuid().optional(),
  fromId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationCreateManyNodeInputSchema: z.ZodType<Prisma.ValidationCreateManyNodeInput> = z.object({
  id: z.string().cuid().optional(),
  expertId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipUpdateWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateWithoutFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  to: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutRelationshipsToNestedInputSchema).optional()
}).strict();

export const NodeRelationshipUncheckedUpdateWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateWithoutFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  toId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyWithoutFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  toId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUpdateWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateWithoutToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  from: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutRelationshipsFromNestedInputSchema).optional()
}).strict();

export const NodeRelationshipUncheckedUpdateWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateWithoutToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  fromId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyWithoutToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  fromId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUpdateWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUpdateWithoutNodeInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  expert: z.lazy(() => UserUpdateOneRequiredWithoutValidationsNestedInputSchema).optional()
}).strict();

export const ValidationUncheckedUpdateWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateWithoutNodeInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  expertId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyWithoutNodeInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  expertId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicUpdateWithoutNodesInputSchema: z.ZodType<Prisma.TopicUpdateWithoutNodesInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicUncheckedUpdateWithoutNodesInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateWithoutNodesInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicUncheckedUpdateManyWithoutNodesInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateManyWithoutNodesInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeUpdateWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutTopicsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutTopicsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateManyWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyWithoutTopicsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairCreateManyAgentInputSchema: z.ZodType<Prisma.QnAPairCreateManyAgentInput> = z.object({
  id: z.string().cuid().optional(),
  question: z.string(),
  answer: z.string(),
  persona: z.lazy(() => PersonaTypeSchema),
  createdAt: z.coerce.date().optional()
}).strict();

export const SessionCreateManyAgentInputSchema: z.ZodType<Prisma.SessionCreateManyAgentInput> = z.object({
  id: z.string().cuid().optional(),
  status: z.lazy(() => SessionStatusSchema).optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  type: z.lazy(() => SessionTypeSchema).optional()
}).strict();

export const QnAPairUpdateWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairUpdateWithoutAgentInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  question: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  answer: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  persona: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => EnumPersonaTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairUncheckedUpdateWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairUncheckedUpdateWithoutAgentInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  question: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  answer: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  persona: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => EnumPersonaTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const QnAPairUncheckedUpdateManyWithoutAgentInputSchema: z.ZodType<Prisma.QnAPairUncheckedUpdateManyWithoutAgentInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  question: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  answer: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  persona: z.union([ z.lazy(() => PersonaTypeSchema),z.lazy(() => EnumPersonaTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionUpdateWithoutAgentInputSchema: z.ZodType<Prisma.SessionUpdateWithoutAgentInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
  messages: z.lazy(() => SessionMessageUpdateManyWithoutSessionNestedInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantUpdateManyWithoutSessionNestedInputSchema).optional()
}).strict();

export const SessionUncheckedUpdateWithoutAgentInputSchema: z.ZodType<Prisma.SessionUncheckedUpdateWithoutAgentInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
  messages: z.lazy(() => SessionMessageUncheckedUpdateManyWithoutSessionNestedInputSchema).optional(),
  participants: z.lazy(() => SessionParticipantUncheckedUpdateManyWithoutSessionNestedInputSchema).optional()
}).strict();

export const SessionUncheckedUpdateManyWithoutAgentInputSchema: z.ZodType<Prisma.SessionUncheckedUpdateManyWithoutAgentInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  status: z.union([ z.lazy(() => SessionStatusSchema),z.lazy(() => EnumSessionStatusFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SessionTypeSchema),z.lazy(() => EnumSessionTypeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionMessageCreateManySessionInputSchema: z.ZodType<Prisma.SessionMessageCreateManySessionInput> = z.object({
  id: z.string().cuid().optional(),
  senderId: z.string().optional().nullable(),
  role: z.lazy(() => RoleTypeSchema),
  content: z.string(),
  createdAt: z.coerce.date().optional()
}).strict();

export const SessionParticipantCreateManySessionInputSchema: z.ZodType<Prisma.SessionParticipantCreateManySessionInput> = z.object({
  id: z.string().cuid().optional(),
  userId: z.string(),
  role: z.lazy(() => SessionRoleSchema),
  joinedAt: z.coerce.date().optional()
}).strict();

export const SessionMessageUpdateWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageUpdateWithoutSessionInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  senderId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  role: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => EnumRoleTypeFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionMessageUncheckedUpdateWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageUncheckedUpdateWithoutSessionInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  senderId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  role: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => EnumRoleTypeFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionMessageUncheckedUpdateManyWithoutSessionInputSchema: z.ZodType<Prisma.SessionMessageUncheckedUpdateManyWithoutSessionInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  senderId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  role: z.union([ z.lazy(() => RoleTypeSchema),z.lazy(() => EnumRoleTypeFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionParticipantUpdateWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantUpdateWithoutSessionInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => EnumSessionRoleFieldUpdateOperationsInputSchema) ]).optional(),
  joinedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionParticipantUncheckedUpdateWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantUncheckedUpdateWithoutSessionInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => EnumSessionRoleFieldUpdateOperationsInputSchema) ]).optional(),
  joinedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const SessionParticipantUncheckedUpdateManyWithoutSessionInputSchema: z.ZodType<Prisma.SessionParticipantUncheckedUpdateManyWithoutSessionInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => SessionRoleSchema),z.lazy(() => EnumSessionRoleFieldUpdateOperationsInputSchema) ]).optional(),
  joinedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

/////////////////////////////////////////
// ARGS
/////////////////////////////////////////

export const TempEmbeddingsTableFindFirstArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindFirstArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithRelationInputSchema.array(),TempEmbeddingsTableOrderByWithRelationInputSchema ]).optional(),
  cursor: TempEmbeddingsTableWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TempEmbeddingsTableScalarFieldEnumSchema,TempEmbeddingsTableScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TempEmbeddingsTableFindFirstOrThrowArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindFirstOrThrowArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithRelationInputSchema.array(),TempEmbeddingsTableOrderByWithRelationInputSchema ]).optional(),
  cursor: TempEmbeddingsTableWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TempEmbeddingsTableScalarFieldEnumSchema,TempEmbeddingsTableScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TempEmbeddingsTableFindManyArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindManyArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithRelationInputSchema.array(),TempEmbeddingsTableOrderByWithRelationInputSchema ]).optional(),
  cursor: TempEmbeddingsTableWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TempEmbeddingsTableScalarFieldEnumSchema,TempEmbeddingsTableScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TempEmbeddingsTableAggregateArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableAggregateArgs> = z.object({
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithRelationInputSchema.array(),TempEmbeddingsTableOrderByWithRelationInputSchema ]).optional(),
  cursor: TempEmbeddingsTableWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableGroupByArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableGroupByArgs> = z.object({
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithAggregationInputSchema.array(),TempEmbeddingsTableOrderByWithAggregationInputSchema ]).optional(),
  by: TempEmbeddingsTableScalarFieldEnumSchema.array(),
  having: TempEmbeddingsTableScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableFindUniqueArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindUniqueArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereUniqueInputSchema,
}).strict() ;

export const TempEmbeddingsTableFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindUniqueOrThrowArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeEmbeddingsFindFirstArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsFindFirstArgs> = z.object({
  select: KnowledgeNodeEmbeddingsSelectSchema.optional(),
  where: KnowledgeNodeEmbeddingsWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema.array(),KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeEmbeddingsWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeEmbeddingsScalarFieldEnumSchema,KnowledgeNodeEmbeddingsScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeEmbeddingsFindFirstOrThrowArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsFindFirstOrThrowArgs> = z.object({
  select: KnowledgeNodeEmbeddingsSelectSchema.optional(),
  where: KnowledgeNodeEmbeddingsWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema.array(),KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeEmbeddingsWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeEmbeddingsScalarFieldEnumSchema,KnowledgeNodeEmbeddingsScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeEmbeddingsFindManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsFindManyArgs> = z.object({
  select: KnowledgeNodeEmbeddingsSelectSchema.optional(),
  where: KnowledgeNodeEmbeddingsWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema.array(),KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeEmbeddingsWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeEmbeddingsScalarFieldEnumSchema,KnowledgeNodeEmbeddingsScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeEmbeddingsAggregateArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsAggregateArgs> = z.object({
  where: KnowledgeNodeEmbeddingsWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema.array(),KnowledgeNodeEmbeddingsOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeEmbeddingsWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const KnowledgeNodeEmbeddingsGroupByArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsGroupByArgs> = z.object({
  where: KnowledgeNodeEmbeddingsWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeEmbeddingsOrderByWithAggregationInputSchema.array(),KnowledgeNodeEmbeddingsOrderByWithAggregationInputSchema ]).optional(),
  by: KnowledgeNodeEmbeddingsScalarFieldEnumSchema.array(),
  having: KnowledgeNodeEmbeddingsScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const KnowledgeNodeEmbeddingsFindUniqueArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsFindUniqueArgs> = z.object({
  select: KnowledgeNodeEmbeddingsSelectSchema.optional(),
  where: KnowledgeNodeEmbeddingsWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeEmbeddingsFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsFindUniqueOrThrowArgs> = z.object({
  select: KnowledgeNodeEmbeddingsSelectSchema.optional(),
  where: KnowledgeNodeEmbeddingsWhereUniqueInputSchema,
}).strict() ;

export const FooFindFirstArgsSchema: z.ZodType<Prisma.FooFindFirstArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithRelationInputSchema.array(),FooOrderByWithRelationInputSchema ]).optional(),
  cursor: FooWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ FooScalarFieldEnumSchema,FooScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const FooFindFirstOrThrowArgsSchema: z.ZodType<Prisma.FooFindFirstOrThrowArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithRelationInputSchema.array(),FooOrderByWithRelationInputSchema ]).optional(),
  cursor: FooWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ FooScalarFieldEnumSchema,FooScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const FooFindManyArgsSchema: z.ZodType<Prisma.FooFindManyArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithRelationInputSchema.array(),FooOrderByWithRelationInputSchema ]).optional(),
  cursor: FooWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ FooScalarFieldEnumSchema,FooScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const FooAggregateArgsSchema: z.ZodType<Prisma.FooAggregateArgs> = z.object({
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithRelationInputSchema.array(),FooOrderByWithRelationInputSchema ]).optional(),
  cursor: FooWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const FooGroupByArgsSchema: z.ZodType<Prisma.FooGroupByArgs> = z.object({
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithAggregationInputSchema.array(),FooOrderByWithAggregationInputSchema ]).optional(),
  by: FooScalarFieldEnumSchema.array(),
  having: FooScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const FooFindUniqueArgsSchema: z.ZodType<Prisma.FooFindUniqueArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereUniqueInputSchema,
}).strict() ;

export const FooFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.FooFindUniqueOrThrowArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereUniqueInputSchema,
}).strict() ;

export const UserFindFirstArgsSchema: z.ZodType<Prisma.UserFindFirstArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithRelationInputSchema.array(),UserOrderByWithRelationInputSchema ]).optional(),
  cursor: UserWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserScalarFieldEnumSchema,UserScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const UserFindFirstOrThrowArgsSchema: z.ZodType<Prisma.UserFindFirstOrThrowArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithRelationInputSchema.array(),UserOrderByWithRelationInputSchema ]).optional(),
  cursor: UserWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserScalarFieldEnumSchema,UserScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const UserFindManyArgsSchema: z.ZodType<Prisma.UserFindManyArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithRelationInputSchema.array(),UserOrderByWithRelationInputSchema ]).optional(),
  cursor: UserWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserScalarFieldEnumSchema,UserScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const UserAggregateArgsSchema: z.ZodType<Prisma.UserAggregateArgs> = z.object({
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithRelationInputSchema.array(),UserOrderByWithRelationInputSchema ]).optional(),
  cursor: UserWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const UserGroupByArgsSchema: z.ZodType<Prisma.UserGroupByArgs> = z.object({
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithAggregationInputSchema.array(),UserOrderByWithAggregationInputSchema ]).optional(),
  by: UserScalarFieldEnumSchema.array(),
  having: UserScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const UserFindUniqueArgsSchema: z.ZodType<Prisma.UserFindUniqueArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export const UserFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.UserFindUniqueOrThrowArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeFindFirstArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindFirstArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithRelationInputSchema.array(),KnowledgeNodeOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeScalarFieldEnumSchema,KnowledgeNodeScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeFindFirstOrThrowArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindFirstOrThrowArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithRelationInputSchema.array(),KnowledgeNodeOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeScalarFieldEnumSchema,KnowledgeNodeScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeFindManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindManyArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithRelationInputSchema.array(),KnowledgeNodeOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeScalarFieldEnumSchema,KnowledgeNodeScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeAggregateArgsSchema: z.ZodType<Prisma.KnowledgeNodeAggregateArgs> = z.object({
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithRelationInputSchema.array(),KnowledgeNodeOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const KnowledgeNodeGroupByArgsSchema: z.ZodType<Prisma.KnowledgeNodeGroupByArgs> = z.object({
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithAggregationInputSchema.array(),KnowledgeNodeOrderByWithAggregationInputSchema ]).optional(),
  by: KnowledgeNodeScalarFieldEnumSchema.array(),
  having: KnowledgeNodeScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const KnowledgeNodeFindUniqueArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindUniqueArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindUniqueOrThrowArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereUniqueInputSchema,
}).strict() ;

export const NodeRelationshipFindFirstArgsSchema: z.ZodType<Prisma.NodeRelationshipFindFirstArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithRelationInputSchema.array(),NodeRelationshipOrderByWithRelationInputSchema ]).optional(),
  cursor: NodeRelationshipWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ NodeRelationshipScalarFieldEnumSchema,NodeRelationshipScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const NodeRelationshipFindFirstOrThrowArgsSchema: z.ZodType<Prisma.NodeRelationshipFindFirstOrThrowArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithRelationInputSchema.array(),NodeRelationshipOrderByWithRelationInputSchema ]).optional(),
  cursor: NodeRelationshipWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ NodeRelationshipScalarFieldEnumSchema,NodeRelationshipScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const NodeRelationshipFindManyArgsSchema: z.ZodType<Prisma.NodeRelationshipFindManyArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithRelationInputSchema.array(),NodeRelationshipOrderByWithRelationInputSchema ]).optional(),
  cursor: NodeRelationshipWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ NodeRelationshipScalarFieldEnumSchema,NodeRelationshipScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const NodeRelationshipAggregateArgsSchema: z.ZodType<Prisma.NodeRelationshipAggregateArgs> = z.object({
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithRelationInputSchema.array(),NodeRelationshipOrderByWithRelationInputSchema ]).optional(),
  cursor: NodeRelationshipWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const NodeRelationshipGroupByArgsSchema: z.ZodType<Prisma.NodeRelationshipGroupByArgs> = z.object({
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithAggregationInputSchema.array(),NodeRelationshipOrderByWithAggregationInputSchema ]).optional(),
  by: NodeRelationshipScalarFieldEnumSchema.array(),
  having: NodeRelationshipScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const NodeRelationshipFindUniqueArgsSchema: z.ZodType<Prisma.NodeRelationshipFindUniqueArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereUniqueInputSchema,
}).strict() ;

export const NodeRelationshipFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.NodeRelationshipFindUniqueOrThrowArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereUniqueInputSchema,
}).strict() ;

export const ValidationFindFirstArgsSchema: z.ZodType<Prisma.ValidationFindFirstArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithRelationInputSchema.array(),ValidationOrderByWithRelationInputSchema ]).optional(),
  cursor: ValidationWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ ValidationScalarFieldEnumSchema,ValidationScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const ValidationFindFirstOrThrowArgsSchema: z.ZodType<Prisma.ValidationFindFirstOrThrowArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithRelationInputSchema.array(),ValidationOrderByWithRelationInputSchema ]).optional(),
  cursor: ValidationWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ ValidationScalarFieldEnumSchema,ValidationScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const ValidationFindManyArgsSchema: z.ZodType<Prisma.ValidationFindManyArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithRelationInputSchema.array(),ValidationOrderByWithRelationInputSchema ]).optional(),
  cursor: ValidationWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ ValidationScalarFieldEnumSchema,ValidationScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const ValidationAggregateArgsSchema: z.ZodType<Prisma.ValidationAggregateArgs> = z.object({
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithRelationInputSchema.array(),ValidationOrderByWithRelationInputSchema ]).optional(),
  cursor: ValidationWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const ValidationGroupByArgsSchema: z.ZodType<Prisma.ValidationGroupByArgs> = z.object({
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithAggregationInputSchema.array(),ValidationOrderByWithAggregationInputSchema ]).optional(),
  by: ValidationScalarFieldEnumSchema.array(),
  having: ValidationScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const ValidationFindUniqueArgsSchema: z.ZodType<Prisma.ValidationFindUniqueArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereUniqueInputSchema,
}).strict() ;

export const ValidationFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.ValidationFindUniqueOrThrowArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereUniqueInputSchema,
}).strict() ;

export const TopicFindFirstArgsSchema: z.ZodType<Prisma.TopicFindFirstArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithRelationInputSchema.array(),TopicOrderByWithRelationInputSchema ]).optional(),
  cursor: TopicWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TopicScalarFieldEnumSchema,TopicScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TopicFindFirstOrThrowArgsSchema: z.ZodType<Prisma.TopicFindFirstOrThrowArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithRelationInputSchema.array(),TopicOrderByWithRelationInputSchema ]).optional(),
  cursor: TopicWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TopicScalarFieldEnumSchema,TopicScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TopicFindManyArgsSchema: z.ZodType<Prisma.TopicFindManyArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithRelationInputSchema.array(),TopicOrderByWithRelationInputSchema ]).optional(),
  cursor: TopicWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TopicScalarFieldEnumSchema,TopicScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TopicAggregateArgsSchema: z.ZodType<Prisma.TopicAggregateArgs> = z.object({
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithRelationInputSchema.array(),TopicOrderByWithRelationInputSchema ]).optional(),
  cursor: TopicWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const TopicGroupByArgsSchema: z.ZodType<Prisma.TopicGroupByArgs> = z.object({
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithAggregationInputSchema.array(),TopicOrderByWithAggregationInputSchema ]).optional(),
  by: TopicScalarFieldEnumSchema.array(),
  having: TopicScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const TopicFindUniqueArgsSchema: z.ZodType<Prisma.TopicFindUniqueArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereUniqueInputSchema,
}).strict() ;

export const TopicFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.TopicFindUniqueOrThrowArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereUniqueInputSchema,
}).strict() ;

export const AgentFindFirstArgsSchema: z.ZodType<Prisma.AgentFindFirstArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  where: AgentWhereInputSchema.optional(),
  orderBy: z.union([ AgentOrderByWithRelationInputSchema.array(),AgentOrderByWithRelationInputSchema ]).optional(),
  cursor: AgentWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ AgentScalarFieldEnumSchema,AgentScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const AgentFindFirstOrThrowArgsSchema: z.ZodType<Prisma.AgentFindFirstOrThrowArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  where: AgentWhereInputSchema.optional(),
  orderBy: z.union([ AgentOrderByWithRelationInputSchema.array(),AgentOrderByWithRelationInputSchema ]).optional(),
  cursor: AgentWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ AgentScalarFieldEnumSchema,AgentScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const AgentFindManyArgsSchema: z.ZodType<Prisma.AgentFindManyArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  where: AgentWhereInputSchema.optional(),
  orderBy: z.union([ AgentOrderByWithRelationInputSchema.array(),AgentOrderByWithRelationInputSchema ]).optional(),
  cursor: AgentWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ AgentScalarFieldEnumSchema,AgentScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const AgentAggregateArgsSchema: z.ZodType<Prisma.AgentAggregateArgs> = z.object({
  where: AgentWhereInputSchema.optional(),
  orderBy: z.union([ AgentOrderByWithRelationInputSchema.array(),AgentOrderByWithRelationInputSchema ]).optional(),
  cursor: AgentWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const AgentGroupByArgsSchema: z.ZodType<Prisma.AgentGroupByArgs> = z.object({
  where: AgentWhereInputSchema.optional(),
  orderBy: z.union([ AgentOrderByWithAggregationInputSchema.array(),AgentOrderByWithAggregationInputSchema ]).optional(),
  by: AgentScalarFieldEnumSchema.array(),
  having: AgentScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const AgentFindUniqueArgsSchema: z.ZodType<Prisma.AgentFindUniqueArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  where: AgentWhereUniqueInputSchema,
}).strict() ;

export const AgentFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.AgentFindUniqueOrThrowArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  where: AgentWhereUniqueInputSchema,
}).strict() ;

export const QnAPairFindFirstArgsSchema: z.ZodType<Prisma.QnAPairFindFirstArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  where: QnAPairWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairOrderByWithRelationInputSchema.array(),QnAPairOrderByWithRelationInputSchema ]).optional(),
  cursor: QnAPairWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ QnAPairScalarFieldEnumSchema,QnAPairScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const QnAPairFindFirstOrThrowArgsSchema: z.ZodType<Prisma.QnAPairFindFirstOrThrowArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  where: QnAPairWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairOrderByWithRelationInputSchema.array(),QnAPairOrderByWithRelationInputSchema ]).optional(),
  cursor: QnAPairWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ QnAPairScalarFieldEnumSchema,QnAPairScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const QnAPairFindManyArgsSchema: z.ZodType<Prisma.QnAPairFindManyArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  where: QnAPairWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairOrderByWithRelationInputSchema.array(),QnAPairOrderByWithRelationInputSchema ]).optional(),
  cursor: QnAPairWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ QnAPairScalarFieldEnumSchema,QnAPairScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const QnAPairAggregateArgsSchema: z.ZodType<Prisma.QnAPairAggregateArgs> = z.object({
  where: QnAPairWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairOrderByWithRelationInputSchema.array(),QnAPairOrderByWithRelationInputSchema ]).optional(),
  cursor: QnAPairWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const QnAPairGroupByArgsSchema: z.ZodType<Prisma.QnAPairGroupByArgs> = z.object({
  where: QnAPairWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairOrderByWithAggregationInputSchema.array(),QnAPairOrderByWithAggregationInputSchema ]).optional(),
  by: QnAPairScalarFieldEnumSchema.array(),
  having: QnAPairScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const QnAPairFindUniqueArgsSchema: z.ZodType<Prisma.QnAPairFindUniqueArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  where: QnAPairWhereUniqueInputSchema,
}).strict() ;

export const QnAPairFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.QnAPairFindUniqueOrThrowArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  where: QnAPairWhereUniqueInputSchema,
}).strict() ;

export const QnAPairEmbeddingFindFirstArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingFindFirstArgs> = z.object({
  select: QnAPairEmbeddingSelectSchema.optional(),
  where: QnAPairEmbeddingWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairEmbeddingOrderByWithRelationInputSchema.array(),QnAPairEmbeddingOrderByWithRelationInputSchema ]).optional(),
  cursor: QnAPairEmbeddingWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ QnAPairEmbeddingScalarFieldEnumSchema,QnAPairEmbeddingScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const QnAPairEmbeddingFindFirstOrThrowArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingFindFirstOrThrowArgs> = z.object({
  select: QnAPairEmbeddingSelectSchema.optional(),
  where: QnAPairEmbeddingWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairEmbeddingOrderByWithRelationInputSchema.array(),QnAPairEmbeddingOrderByWithRelationInputSchema ]).optional(),
  cursor: QnAPairEmbeddingWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ QnAPairEmbeddingScalarFieldEnumSchema,QnAPairEmbeddingScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const QnAPairEmbeddingFindManyArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingFindManyArgs> = z.object({
  select: QnAPairEmbeddingSelectSchema.optional(),
  where: QnAPairEmbeddingWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairEmbeddingOrderByWithRelationInputSchema.array(),QnAPairEmbeddingOrderByWithRelationInputSchema ]).optional(),
  cursor: QnAPairEmbeddingWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ QnAPairEmbeddingScalarFieldEnumSchema,QnAPairEmbeddingScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const QnAPairEmbeddingAggregateArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingAggregateArgs> = z.object({
  where: QnAPairEmbeddingWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairEmbeddingOrderByWithRelationInputSchema.array(),QnAPairEmbeddingOrderByWithRelationInputSchema ]).optional(),
  cursor: QnAPairEmbeddingWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const QnAPairEmbeddingGroupByArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingGroupByArgs> = z.object({
  where: QnAPairEmbeddingWhereInputSchema.optional(),
  orderBy: z.union([ QnAPairEmbeddingOrderByWithAggregationInputSchema.array(),QnAPairEmbeddingOrderByWithAggregationInputSchema ]).optional(),
  by: QnAPairEmbeddingScalarFieldEnumSchema.array(),
  having: QnAPairEmbeddingScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const QnAPairEmbeddingFindUniqueArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingFindUniqueArgs> = z.object({
  select: QnAPairEmbeddingSelectSchema.optional(),
  where: QnAPairEmbeddingWhereUniqueInputSchema,
}).strict() ;

export const QnAPairEmbeddingFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingFindUniqueOrThrowArgs> = z.object({
  select: QnAPairEmbeddingSelectSchema.optional(),
  where: QnAPairEmbeddingWhereUniqueInputSchema,
}).strict() ;

export const SessionFindFirstArgsSchema: z.ZodType<Prisma.SessionFindFirstArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  where: SessionWhereInputSchema.optional(),
  orderBy: z.union([ SessionOrderByWithRelationInputSchema.array(),SessionOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionScalarFieldEnumSchema,SessionScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionFindFirstOrThrowArgsSchema: z.ZodType<Prisma.SessionFindFirstOrThrowArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  where: SessionWhereInputSchema.optional(),
  orderBy: z.union([ SessionOrderByWithRelationInputSchema.array(),SessionOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionScalarFieldEnumSchema,SessionScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionFindManyArgsSchema: z.ZodType<Prisma.SessionFindManyArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  where: SessionWhereInputSchema.optional(),
  orderBy: z.union([ SessionOrderByWithRelationInputSchema.array(),SessionOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionScalarFieldEnumSchema,SessionScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionAggregateArgsSchema: z.ZodType<Prisma.SessionAggregateArgs> = z.object({
  where: SessionWhereInputSchema.optional(),
  orderBy: z.union([ SessionOrderByWithRelationInputSchema.array(),SessionOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const SessionGroupByArgsSchema: z.ZodType<Prisma.SessionGroupByArgs> = z.object({
  where: SessionWhereInputSchema.optional(),
  orderBy: z.union([ SessionOrderByWithAggregationInputSchema.array(),SessionOrderByWithAggregationInputSchema ]).optional(),
  by: SessionScalarFieldEnumSchema.array(),
  having: SessionScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const SessionFindUniqueArgsSchema: z.ZodType<Prisma.SessionFindUniqueArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  where: SessionWhereUniqueInputSchema,
}).strict() ;

export const SessionFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.SessionFindUniqueOrThrowArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  where: SessionWhereUniqueInputSchema,
}).strict() ;

export const SessionMessageFindFirstArgsSchema: z.ZodType<Prisma.SessionMessageFindFirstArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  where: SessionMessageWhereInputSchema.optional(),
  orderBy: z.union([ SessionMessageOrderByWithRelationInputSchema.array(),SessionMessageOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionMessageWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionMessageScalarFieldEnumSchema,SessionMessageScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionMessageFindFirstOrThrowArgsSchema: z.ZodType<Prisma.SessionMessageFindFirstOrThrowArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  where: SessionMessageWhereInputSchema.optional(),
  orderBy: z.union([ SessionMessageOrderByWithRelationInputSchema.array(),SessionMessageOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionMessageWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionMessageScalarFieldEnumSchema,SessionMessageScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionMessageFindManyArgsSchema: z.ZodType<Prisma.SessionMessageFindManyArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  where: SessionMessageWhereInputSchema.optional(),
  orderBy: z.union([ SessionMessageOrderByWithRelationInputSchema.array(),SessionMessageOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionMessageWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionMessageScalarFieldEnumSchema,SessionMessageScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionMessageAggregateArgsSchema: z.ZodType<Prisma.SessionMessageAggregateArgs> = z.object({
  where: SessionMessageWhereInputSchema.optional(),
  orderBy: z.union([ SessionMessageOrderByWithRelationInputSchema.array(),SessionMessageOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionMessageWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const SessionMessageGroupByArgsSchema: z.ZodType<Prisma.SessionMessageGroupByArgs> = z.object({
  where: SessionMessageWhereInputSchema.optional(),
  orderBy: z.union([ SessionMessageOrderByWithAggregationInputSchema.array(),SessionMessageOrderByWithAggregationInputSchema ]).optional(),
  by: SessionMessageScalarFieldEnumSchema.array(),
  having: SessionMessageScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const SessionMessageFindUniqueArgsSchema: z.ZodType<Prisma.SessionMessageFindUniqueArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  where: SessionMessageWhereUniqueInputSchema,
}).strict() ;

export const SessionMessageFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.SessionMessageFindUniqueOrThrowArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  where: SessionMessageWhereUniqueInputSchema,
}).strict() ;

export const SessionParticipantFindFirstArgsSchema: z.ZodType<Prisma.SessionParticipantFindFirstArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  where: SessionParticipantWhereInputSchema.optional(),
  orderBy: z.union([ SessionParticipantOrderByWithRelationInputSchema.array(),SessionParticipantOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionParticipantWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionParticipantScalarFieldEnumSchema,SessionParticipantScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionParticipantFindFirstOrThrowArgsSchema: z.ZodType<Prisma.SessionParticipantFindFirstOrThrowArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  where: SessionParticipantWhereInputSchema.optional(),
  orderBy: z.union([ SessionParticipantOrderByWithRelationInputSchema.array(),SessionParticipantOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionParticipantWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionParticipantScalarFieldEnumSchema,SessionParticipantScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionParticipantFindManyArgsSchema: z.ZodType<Prisma.SessionParticipantFindManyArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  where: SessionParticipantWhereInputSchema.optional(),
  orderBy: z.union([ SessionParticipantOrderByWithRelationInputSchema.array(),SessionParticipantOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionParticipantWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ SessionParticipantScalarFieldEnumSchema,SessionParticipantScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const SessionParticipantAggregateArgsSchema: z.ZodType<Prisma.SessionParticipantAggregateArgs> = z.object({
  where: SessionParticipantWhereInputSchema.optional(),
  orderBy: z.union([ SessionParticipantOrderByWithRelationInputSchema.array(),SessionParticipantOrderByWithRelationInputSchema ]).optional(),
  cursor: SessionParticipantWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const SessionParticipantGroupByArgsSchema: z.ZodType<Prisma.SessionParticipantGroupByArgs> = z.object({
  where: SessionParticipantWhereInputSchema.optional(),
  orderBy: z.union([ SessionParticipantOrderByWithAggregationInputSchema.array(),SessionParticipantOrderByWithAggregationInputSchema ]).optional(),
  by: SessionParticipantScalarFieldEnumSchema.array(),
  having: SessionParticipantScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const SessionParticipantFindUniqueArgsSchema: z.ZodType<Prisma.SessionParticipantFindUniqueArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  where: SessionParticipantWhereUniqueInputSchema,
}).strict() ;

export const SessionParticipantFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.SessionParticipantFindUniqueOrThrowArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  where: SessionParticipantWhereUniqueInputSchema,
}).strict() ;

export const TempEmbeddingsTableDeleteArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableDeleteArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereUniqueInputSchema,
}).strict() ;

export const TempEmbeddingsTableUpdateArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  data: z.union([ TempEmbeddingsTableUpdateInputSchema,TempEmbeddingsTableUncheckedUpdateInputSchema ]),
  where: TempEmbeddingsTableWhereUniqueInputSchema,
}).strict() ;

export const TempEmbeddingsTableUpdateManyArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateManyArgs> = z.object({
  data: z.union([ TempEmbeddingsTableUpdateManyMutationInputSchema,TempEmbeddingsTableUncheckedUpdateManyInputSchema ]),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateManyAndReturnArgs> = z.object({
  data: z.union([ TempEmbeddingsTableUpdateManyMutationInputSchema,TempEmbeddingsTableUncheckedUpdateManyInputSchema ]),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableDeleteManyArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableDeleteManyArgs> = z.object({
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeEmbeddingsDeleteArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsDeleteArgs> = z.object({
  select: KnowledgeNodeEmbeddingsSelectSchema.optional(),
  where: KnowledgeNodeEmbeddingsWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeEmbeddingsUpdateArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsUpdateArgs> = z.object({
  select: KnowledgeNodeEmbeddingsSelectSchema.optional(),
  data: z.union([ KnowledgeNodeEmbeddingsUpdateInputSchema,KnowledgeNodeEmbeddingsUncheckedUpdateInputSchema ]),
  where: KnowledgeNodeEmbeddingsWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeEmbeddingsUpdateManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsUpdateManyArgs> = z.object({
  data: z.union([ KnowledgeNodeEmbeddingsUpdateManyMutationInputSchema,KnowledgeNodeEmbeddingsUncheckedUpdateManyInputSchema ]),
  where: KnowledgeNodeEmbeddingsWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeEmbeddingsUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsUpdateManyAndReturnArgs> = z.object({
  data: z.union([ KnowledgeNodeEmbeddingsUpdateManyMutationInputSchema,KnowledgeNodeEmbeddingsUncheckedUpdateManyInputSchema ]),
  where: KnowledgeNodeEmbeddingsWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeEmbeddingsDeleteManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeEmbeddingsDeleteManyArgs> = z.object({
  where: KnowledgeNodeEmbeddingsWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const FooCreateArgsSchema: z.ZodType<Prisma.FooCreateArgs> = z.object({
  select: FooSelectSchema.optional(),
  data: z.union([ FooCreateInputSchema,FooUncheckedCreateInputSchema ]),
}).strict() ;

export const FooUpsertArgsSchema: z.ZodType<Prisma.FooUpsertArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereUniqueInputSchema,
  create: z.union([ FooCreateInputSchema,FooUncheckedCreateInputSchema ]),
  update: z.union([ FooUpdateInputSchema,FooUncheckedUpdateInputSchema ]),
}).strict() ;

export const FooCreateManyArgsSchema: z.ZodType<Prisma.FooCreateManyArgs> = z.object({
  data: z.union([ FooCreateManyInputSchema,FooCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const FooCreateManyAndReturnArgsSchema: z.ZodType<Prisma.FooCreateManyAndReturnArgs> = z.object({
  data: z.union([ FooCreateManyInputSchema,FooCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const FooDeleteArgsSchema: z.ZodType<Prisma.FooDeleteArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereUniqueInputSchema,
}).strict() ;

export const FooUpdateArgsSchema: z.ZodType<Prisma.FooUpdateArgs> = z.object({
  select: FooSelectSchema.optional(),
  data: z.union([ FooUpdateInputSchema,FooUncheckedUpdateInputSchema ]),
  where: FooWhereUniqueInputSchema,
}).strict() ;

export const FooUpdateManyArgsSchema: z.ZodType<Prisma.FooUpdateManyArgs> = z.object({
  data: z.union([ FooUpdateManyMutationInputSchema,FooUncheckedUpdateManyInputSchema ]),
  where: FooWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const FooUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.FooUpdateManyAndReturnArgs> = z.object({
  data: z.union([ FooUpdateManyMutationInputSchema,FooUncheckedUpdateManyInputSchema ]),
  where: FooWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const FooDeleteManyArgsSchema: z.ZodType<Prisma.FooDeleteManyArgs> = z.object({
  where: FooWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const UserCreateArgsSchema: z.ZodType<Prisma.UserCreateArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  data: z.union([ UserCreateInputSchema,UserUncheckedCreateInputSchema ]),
}).strict() ;

export const UserUpsertArgsSchema: z.ZodType<Prisma.UserUpsertArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereUniqueInputSchema,
  create: z.union([ UserCreateInputSchema,UserUncheckedCreateInputSchema ]),
  update: z.union([ UserUpdateInputSchema,UserUncheckedUpdateInputSchema ]),
}).strict() ;

export const UserCreateManyArgsSchema: z.ZodType<Prisma.UserCreateManyArgs> = z.object({
  data: z.union([ UserCreateManyInputSchema,UserCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const UserCreateManyAndReturnArgsSchema: z.ZodType<Prisma.UserCreateManyAndReturnArgs> = z.object({
  data: z.union([ UserCreateManyInputSchema,UserCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const UserDeleteArgsSchema: z.ZodType<Prisma.UserDeleteArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export const UserUpdateArgsSchema: z.ZodType<Prisma.UserUpdateArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  data: z.union([ UserUpdateInputSchema,UserUncheckedUpdateInputSchema ]),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export const UserUpdateManyArgsSchema: z.ZodType<Prisma.UserUpdateManyArgs> = z.object({
  data: z.union([ UserUpdateManyMutationInputSchema,UserUncheckedUpdateManyInputSchema ]),
  where: UserWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const UserUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.UserUpdateManyAndReturnArgs> = z.object({
  data: z.union([ UserUpdateManyMutationInputSchema,UserUncheckedUpdateManyInputSchema ]),
  where: UserWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const UserDeleteManyArgsSchema: z.ZodType<Prisma.UserDeleteManyArgs> = z.object({
  where: UserWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeCreateArgsSchema: z.ZodType<Prisma.KnowledgeNodeCreateArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  data: z.union([ KnowledgeNodeCreateInputSchema,KnowledgeNodeUncheckedCreateInputSchema ]),
}).strict() ;

export const KnowledgeNodeUpsertArgsSchema: z.ZodType<Prisma.KnowledgeNodeUpsertArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereUniqueInputSchema,
  create: z.union([ KnowledgeNodeCreateInputSchema,KnowledgeNodeUncheckedCreateInputSchema ]),
  update: z.union([ KnowledgeNodeUpdateInputSchema,KnowledgeNodeUncheckedUpdateInputSchema ]),
}).strict() ;

export const KnowledgeNodeCreateManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyArgs> = z.object({
  data: z.union([ KnowledgeNodeCreateManyInputSchema,KnowledgeNodeCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const KnowledgeNodeCreateManyAndReturnArgsSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyAndReturnArgs> = z.object({
  data: z.union([ KnowledgeNodeCreateManyInputSchema,KnowledgeNodeCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const KnowledgeNodeDeleteArgsSchema: z.ZodType<Prisma.KnowledgeNodeDeleteArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeUpdateArgsSchema: z.ZodType<Prisma.KnowledgeNodeUpdateArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  data: z.union([ KnowledgeNodeUpdateInputSchema,KnowledgeNodeUncheckedUpdateInputSchema ]),
  where: KnowledgeNodeWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeUpdateManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyArgs> = z.object({
  data: z.union([ KnowledgeNodeUpdateManyMutationInputSchema,KnowledgeNodeUncheckedUpdateManyInputSchema ]),
  where: KnowledgeNodeWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyAndReturnArgs> = z.object({
  data: z.union([ KnowledgeNodeUpdateManyMutationInputSchema,KnowledgeNodeUncheckedUpdateManyInputSchema ]),
  where: KnowledgeNodeWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeDeleteManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeDeleteManyArgs> = z.object({
  where: KnowledgeNodeWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const NodeRelationshipCreateArgsSchema: z.ZodType<Prisma.NodeRelationshipCreateArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  data: z.union([ NodeRelationshipCreateInputSchema,NodeRelationshipUncheckedCreateInputSchema ]),
}).strict() ;

export const NodeRelationshipUpsertArgsSchema: z.ZodType<Prisma.NodeRelationshipUpsertArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereUniqueInputSchema,
  create: z.union([ NodeRelationshipCreateInputSchema,NodeRelationshipUncheckedCreateInputSchema ]),
  update: z.union([ NodeRelationshipUpdateInputSchema,NodeRelationshipUncheckedUpdateInputSchema ]),
}).strict() ;

export const NodeRelationshipCreateManyArgsSchema: z.ZodType<Prisma.NodeRelationshipCreateManyArgs> = z.object({
  data: z.union([ NodeRelationshipCreateManyInputSchema,NodeRelationshipCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const NodeRelationshipCreateManyAndReturnArgsSchema: z.ZodType<Prisma.NodeRelationshipCreateManyAndReturnArgs> = z.object({
  data: z.union([ NodeRelationshipCreateManyInputSchema,NodeRelationshipCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const NodeRelationshipDeleteArgsSchema: z.ZodType<Prisma.NodeRelationshipDeleteArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereUniqueInputSchema,
}).strict() ;

export const NodeRelationshipUpdateArgsSchema: z.ZodType<Prisma.NodeRelationshipUpdateArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  data: z.union([ NodeRelationshipUpdateInputSchema,NodeRelationshipUncheckedUpdateInputSchema ]),
  where: NodeRelationshipWhereUniqueInputSchema,
}).strict() ;

export const NodeRelationshipUpdateManyArgsSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyArgs> = z.object({
  data: z.union([ NodeRelationshipUpdateManyMutationInputSchema,NodeRelationshipUncheckedUpdateManyInputSchema ]),
  where: NodeRelationshipWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const NodeRelationshipUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyAndReturnArgs> = z.object({
  data: z.union([ NodeRelationshipUpdateManyMutationInputSchema,NodeRelationshipUncheckedUpdateManyInputSchema ]),
  where: NodeRelationshipWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const NodeRelationshipDeleteManyArgsSchema: z.ZodType<Prisma.NodeRelationshipDeleteManyArgs> = z.object({
  where: NodeRelationshipWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const ValidationCreateArgsSchema: z.ZodType<Prisma.ValidationCreateArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  data: z.union([ ValidationCreateInputSchema,ValidationUncheckedCreateInputSchema ]),
}).strict() ;

export const ValidationUpsertArgsSchema: z.ZodType<Prisma.ValidationUpsertArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereUniqueInputSchema,
  create: z.union([ ValidationCreateInputSchema,ValidationUncheckedCreateInputSchema ]),
  update: z.union([ ValidationUpdateInputSchema,ValidationUncheckedUpdateInputSchema ]),
}).strict() ;

export const ValidationCreateManyArgsSchema: z.ZodType<Prisma.ValidationCreateManyArgs> = z.object({
  data: z.union([ ValidationCreateManyInputSchema,ValidationCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const ValidationCreateManyAndReturnArgsSchema: z.ZodType<Prisma.ValidationCreateManyAndReturnArgs> = z.object({
  data: z.union([ ValidationCreateManyInputSchema,ValidationCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const ValidationDeleteArgsSchema: z.ZodType<Prisma.ValidationDeleteArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereUniqueInputSchema,
}).strict() ;

export const ValidationUpdateArgsSchema: z.ZodType<Prisma.ValidationUpdateArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  data: z.union([ ValidationUpdateInputSchema,ValidationUncheckedUpdateInputSchema ]),
  where: ValidationWhereUniqueInputSchema,
}).strict() ;

export const ValidationUpdateManyArgsSchema: z.ZodType<Prisma.ValidationUpdateManyArgs> = z.object({
  data: z.union([ ValidationUpdateManyMutationInputSchema,ValidationUncheckedUpdateManyInputSchema ]),
  where: ValidationWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const ValidationUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.ValidationUpdateManyAndReturnArgs> = z.object({
  data: z.union([ ValidationUpdateManyMutationInputSchema,ValidationUncheckedUpdateManyInputSchema ]),
  where: ValidationWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const ValidationDeleteManyArgsSchema: z.ZodType<Prisma.ValidationDeleteManyArgs> = z.object({
  where: ValidationWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TopicCreateArgsSchema: z.ZodType<Prisma.TopicCreateArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  data: z.union([ TopicCreateInputSchema,TopicUncheckedCreateInputSchema ]),
}).strict() ;

export const TopicUpsertArgsSchema: z.ZodType<Prisma.TopicUpsertArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereUniqueInputSchema,
  create: z.union([ TopicCreateInputSchema,TopicUncheckedCreateInputSchema ]),
  update: z.union([ TopicUpdateInputSchema,TopicUncheckedUpdateInputSchema ]),
}).strict() ;

export const TopicCreateManyArgsSchema: z.ZodType<Prisma.TopicCreateManyArgs> = z.object({
  data: z.union([ TopicCreateManyInputSchema,TopicCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const TopicCreateManyAndReturnArgsSchema: z.ZodType<Prisma.TopicCreateManyAndReturnArgs> = z.object({
  data: z.union([ TopicCreateManyInputSchema,TopicCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const TopicDeleteArgsSchema: z.ZodType<Prisma.TopicDeleteArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereUniqueInputSchema,
}).strict() ;

export const TopicUpdateArgsSchema: z.ZodType<Prisma.TopicUpdateArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  data: z.union([ TopicUpdateInputSchema,TopicUncheckedUpdateInputSchema ]),
  where: TopicWhereUniqueInputSchema,
}).strict() ;

export const TopicUpdateManyArgsSchema: z.ZodType<Prisma.TopicUpdateManyArgs> = z.object({
  data: z.union([ TopicUpdateManyMutationInputSchema,TopicUncheckedUpdateManyInputSchema ]),
  where: TopicWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TopicUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.TopicUpdateManyAndReturnArgs> = z.object({
  data: z.union([ TopicUpdateManyMutationInputSchema,TopicUncheckedUpdateManyInputSchema ]),
  where: TopicWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TopicDeleteManyArgsSchema: z.ZodType<Prisma.TopicDeleteManyArgs> = z.object({
  where: TopicWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const AgentCreateArgsSchema: z.ZodType<Prisma.AgentCreateArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  data: z.union([ AgentCreateInputSchema,AgentUncheckedCreateInputSchema ]),
}).strict() ;

export const AgentUpsertArgsSchema: z.ZodType<Prisma.AgentUpsertArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  where: AgentWhereUniqueInputSchema,
  create: z.union([ AgentCreateInputSchema,AgentUncheckedCreateInputSchema ]),
  update: z.union([ AgentUpdateInputSchema,AgentUncheckedUpdateInputSchema ]),
}).strict() ;

export const AgentCreateManyArgsSchema: z.ZodType<Prisma.AgentCreateManyArgs> = z.object({
  data: z.union([ AgentCreateManyInputSchema,AgentCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const AgentCreateManyAndReturnArgsSchema: z.ZodType<Prisma.AgentCreateManyAndReturnArgs> = z.object({
  data: z.union([ AgentCreateManyInputSchema,AgentCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const AgentDeleteArgsSchema: z.ZodType<Prisma.AgentDeleteArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  where: AgentWhereUniqueInputSchema,
}).strict() ;

export const AgentUpdateArgsSchema: z.ZodType<Prisma.AgentUpdateArgs> = z.object({
  select: AgentSelectSchema.optional(),
  include: AgentIncludeSchema.optional(),
  data: z.union([ AgentUpdateInputSchema,AgentUncheckedUpdateInputSchema ]),
  where: AgentWhereUniqueInputSchema,
}).strict() ;

export const AgentUpdateManyArgsSchema: z.ZodType<Prisma.AgentUpdateManyArgs> = z.object({
  data: z.union([ AgentUpdateManyMutationInputSchema,AgentUncheckedUpdateManyInputSchema ]),
  where: AgentWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const AgentUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.AgentUpdateManyAndReturnArgs> = z.object({
  data: z.union([ AgentUpdateManyMutationInputSchema,AgentUncheckedUpdateManyInputSchema ]),
  where: AgentWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const AgentDeleteManyArgsSchema: z.ZodType<Prisma.AgentDeleteManyArgs> = z.object({
  where: AgentWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const QnAPairCreateArgsSchema: z.ZodType<Prisma.QnAPairCreateArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  data: z.union([ QnAPairCreateInputSchema,QnAPairUncheckedCreateInputSchema ]),
}).strict() ;

export const QnAPairUpsertArgsSchema: z.ZodType<Prisma.QnAPairUpsertArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  where: QnAPairWhereUniqueInputSchema,
  create: z.union([ QnAPairCreateInputSchema,QnAPairUncheckedCreateInputSchema ]),
  update: z.union([ QnAPairUpdateInputSchema,QnAPairUncheckedUpdateInputSchema ]),
}).strict() ;

export const QnAPairCreateManyArgsSchema: z.ZodType<Prisma.QnAPairCreateManyArgs> = z.object({
  data: z.union([ QnAPairCreateManyInputSchema,QnAPairCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const QnAPairCreateManyAndReturnArgsSchema: z.ZodType<Prisma.QnAPairCreateManyAndReturnArgs> = z.object({
  data: z.union([ QnAPairCreateManyInputSchema,QnAPairCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const QnAPairDeleteArgsSchema: z.ZodType<Prisma.QnAPairDeleteArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  where: QnAPairWhereUniqueInputSchema,
}).strict() ;

export const QnAPairUpdateArgsSchema: z.ZodType<Prisma.QnAPairUpdateArgs> = z.object({
  select: QnAPairSelectSchema.optional(),
  include: QnAPairIncludeSchema.optional(),
  data: z.union([ QnAPairUpdateInputSchema,QnAPairUncheckedUpdateInputSchema ]),
  where: QnAPairWhereUniqueInputSchema,
}).strict() ;

export const QnAPairUpdateManyArgsSchema: z.ZodType<Prisma.QnAPairUpdateManyArgs> = z.object({
  data: z.union([ QnAPairUpdateManyMutationInputSchema,QnAPairUncheckedUpdateManyInputSchema ]),
  where: QnAPairWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const QnAPairUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.QnAPairUpdateManyAndReturnArgs> = z.object({
  data: z.union([ QnAPairUpdateManyMutationInputSchema,QnAPairUncheckedUpdateManyInputSchema ]),
  where: QnAPairWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const QnAPairDeleteManyArgsSchema: z.ZodType<Prisma.QnAPairDeleteManyArgs> = z.object({
  where: QnAPairWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const QnAPairEmbeddingDeleteArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingDeleteArgs> = z.object({
  select: QnAPairEmbeddingSelectSchema.optional(),
  where: QnAPairEmbeddingWhereUniqueInputSchema,
}).strict() ;

export const QnAPairEmbeddingUpdateArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingUpdateArgs> = z.object({
  select: QnAPairEmbeddingSelectSchema.optional(),
  data: z.union([ QnAPairEmbeddingUpdateInputSchema,QnAPairEmbeddingUncheckedUpdateInputSchema ]),
  where: QnAPairEmbeddingWhereUniqueInputSchema,
}).strict() ;

export const QnAPairEmbeddingUpdateManyArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingUpdateManyArgs> = z.object({
  data: z.union([ QnAPairEmbeddingUpdateManyMutationInputSchema,QnAPairEmbeddingUncheckedUpdateManyInputSchema ]),
  where: QnAPairEmbeddingWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const QnAPairEmbeddingUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingUpdateManyAndReturnArgs> = z.object({
  data: z.union([ QnAPairEmbeddingUpdateManyMutationInputSchema,QnAPairEmbeddingUncheckedUpdateManyInputSchema ]),
  where: QnAPairEmbeddingWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const QnAPairEmbeddingDeleteManyArgsSchema: z.ZodType<Prisma.QnAPairEmbeddingDeleteManyArgs> = z.object({
  where: QnAPairEmbeddingWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionCreateArgsSchema: z.ZodType<Prisma.SessionCreateArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  data: z.union([ SessionCreateInputSchema,SessionUncheckedCreateInputSchema ]),
}).strict() ;

export const SessionUpsertArgsSchema: z.ZodType<Prisma.SessionUpsertArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  where: SessionWhereUniqueInputSchema,
  create: z.union([ SessionCreateInputSchema,SessionUncheckedCreateInputSchema ]),
  update: z.union([ SessionUpdateInputSchema,SessionUncheckedUpdateInputSchema ]),
}).strict() ;

export const SessionCreateManyArgsSchema: z.ZodType<Prisma.SessionCreateManyArgs> = z.object({
  data: z.union([ SessionCreateManyInputSchema,SessionCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const SessionCreateManyAndReturnArgsSchema: z.ZodType<Prisma.SessionCreateManyAndReturnArgs> = z.object({
  data: z.union([ SessionCreateManyInputSchema,SessionCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const SessionDeleteArgsSchema: z.ZodType<Prisma.SessionDeleteArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  where: SessionWhereUniqueInputSchema,
}).strict() ;

export const SessionUpdateArgsSchema: z.ZodType<Prisma.SessionUpdateArgs> = z.object({
  select: SessionSelectSchema.optional(),
  include: SessionIncludeSchema.optional(),
  data: z.union([ SessionUpdateInputSchema,SessionUncheckedUpdateInputSchema ]),
  where: SessionWhereUniqueInputSchema,
}).strict() ;

export const SessionUpdateManyArgsSchema: z.ZodType<Prisma.SessionUpdateManyArgs> = z.object({
  data: z.union([ SessionUpdateManyMutationInputSchema,SessionUncheckedUpdateManyInputSchema ]),
  where: SessionWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.SessionUpdateManyAndReturnArgs> = z.object({
  data: z.union([ SessionUpdateManyMutationInputSchema,SessionUncheckedUpdateManyInputSchema ]),
  where: SessionWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionDeleteManyArgsSchema: z.ZodType<Prisma.SessionDeleteManyArgs> = z.object({
  where: SessionWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionMessageCreateArgsSchema: z.ZodType<Prisma.SessionMessageCreateArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  data: z.union([ SessionMessageCreateInputSchema,SessionMessageUncheckedCreateInputSchema ]),
}).strict() ;

export const SessionMessageUpsertArgsSchema: z.ZodType<Prisma.SessionMessageUpsertArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  where: SessionMessageWhereUniqueInputSchema,
  create: z.union([ SessionMessageCreateInputSchema,SessionMessageUncheckedCreateInputSchema ]),
  update: z.union([ SessionMessageUpdateInputSchema,SessionMessageUncheckedUpdateInputSchema ]),
}).strict() ;

export const SessionMessageCreateManyArgsSchema: z.ZodType<Prisma.SessionMessageCreateManyArgs> = z.object({
  data: z.union([ SessionMessageCreateManyInputSchema,SessionMessageCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const SessionMessageCreateManyAndReturnArgsSchema: z.ZodType<Prisma.SessionMessageCreateManyAndReturnArgs> = z.object({
  data: z.union([ SessionMessageCreateManyInputSchema,SessionMessageCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const SessionMessageDeleteArgsSchema: z.ZodType<Prisma.SessionMessageDeleteArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  where: SessionMessageWhereUniqueInputSchema,
}).strict() ;

export const SessionMessageUpdateArgsSchema: z.ZodType<Prisma.SessionMessageUpdateArgs> = z.object({
  select: SessionMessageSelectSchema.optional(),
  include: SessionMessageIncludeSchema.optional(),
  data: z.union([ SessionMessageUpdateInputSchema,SessionMessageUncheckedUpdateInputSchema ]),
  where: SessionMessageWhereUniqueInputSchema,
}).strict() ;

export const SessionMessageUpdateManyArgsSchema: z.ZodType<Prisma.SessionMessageUpdateManyArgs> = z.object({
  data: z.union([ SessionMessageUpdateManyMutationInputSchema,SessionMessageUncheckedUpdateManyInputSchema ]),
  where: SessionMessageWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionMessageUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.SessionMessageUpdateManyAndReturnArgs> = z.object({
  data: z.union([ SessionMessageUpdateManyMutationInputSchema,SessionMessageUncheckedUpdateManyInputSchema ]),
  where: SessionMessageWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionMessageDeleteManyArgsSchema: z.ZodType<Prisma.SessionMessageDeleteManyArgs> = z.object({
  where: SessionMessageWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionParticipantCreateArgsSchema: z.ZodType<Prisma.SessionParticipantCreateArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  data: z.union([ SessionParticipantCreateInputSchema,SessionParticipantUncheckedCreateInputSchema ]),
}).strict() ;

export const SessionParticipantUpsertArgsSchema: z.ZodType<Prisma.SessionParticipantUpsertArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  where: SessionParticipantWhereUniqueInputSchema,
  create: z.union([ SessionParticipantCreateInputSchema,SessionParticipantUncheckedCreateInputSchema ]),
  update: z.union([ SessionParticipantUpdateInputSchema,SessionParticipantUncheckedUpdateInputSchema ]),
}).strict() ;

export const SessionParticipantCreateManyArgsSchema: z.ZodType<Prisma.SessionParticipantCreateManyArgs> = z.object({
  data: z.union([ SessionParticipantCreateManyInputSchema,SessionParticipantCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const SessionParticipantCreateManyAndReturnArgsSchema: z.ZodType<Prisma.SessionParticipantCreateManyAndReturnArgs> = z.object({
  data: z.union([ SessionParticipantCreateManyInputSchema,SessionParticipantCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const SessionParticipantDeleteArgsSchema: z.ZodType<Prisma.SessionParticipantDeleteArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  where: SessionParticipantWhereUniqueInputSchema,
}).strict() ;

export const SessionParticipantUpdateArgsSchema: z.ZodType<Prisma.SessionParticipantUpdateArgs> = z.object({
  select: SessionParticipantSelectSchema.optional(),
  include: SessionParticipantIncludeSchema.optional(),
  data: z.union([ SessionParticipantUpdateInputSchema,SessionParticipantUncheckedUpdateInputSchema ]),
  where: SessionParticipantWhereUniqueInputSchema,
}).strict() ;

export const SessionParticipantUpdateManyArgsSchema: z.ZodType<Prisma.SessionParticipantUpdateManyArgs> = z.object({
  data: z.union([ SessionParticipantUpdateManyMutationInputSchema,SessionParticipantUncheckedUpdateManyInputSchema ]),
  where: SessionParticipantWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionParticipantUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.SessionParticipantUpdateManyAndReturnArgs> = z.object({
  data: z.union([ SessionParticipantUpdateManyMutationInputSchema,SessionParticipantUncheckedUpdateManyInputSchema ]),
  where: SessionParticipantWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const SessionParticipantDeleteManyArgsSchema: z.ZodType<Prisma.SessionParticipantDeleteManyArgs> = z.object({
  where: SessionParticipantWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;