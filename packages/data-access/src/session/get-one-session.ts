import { prisma } from '@repo/database';
import type { Prisma, Session } from '@repo/database';

export async function getOneSession<TInclude extends Prisma.SessionInclude | undefined = undefined>(
  filter: Prisma.SessionWhereInput,
  include?: TInclude,
): Promise<
  TInclude extends Prisma.SessionInclude
    ? Prisma.SessionGetPayload<{ include: TInclude }> | null
    : Session | null
> {
  return await prisma.session.findFirst({
    where: filter,
    include,
  }) as TInclude extends Prisma.SessionInclude
    ? Prisma.SessionGetPayload<{ include: TInclude }> | null
    : Session | null;
}
