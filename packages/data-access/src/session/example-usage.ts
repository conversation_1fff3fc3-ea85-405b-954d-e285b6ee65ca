import { getOneSession } from './get-one-session';

// Example usage demonstrating dynamic typing

async function examples() {
  // Example 1: Without include - returns Session | null
  const basicSession = await getOneSession({ id: 'some-id' });
  // TypeScript knows this is Session | null
  console.log(basicSession?.agentId); // ✅ agentId exists on Session
  // console.log(basicSession?.agent); // ❌ TypeScript error - agent not included

  // Example 2: With include - returns SessionGetPayload with included relations
  const sessionWithAgent = await getOneSession(
    { id: 'some-id' },
    { agent: true }
  );
  // TypeScript knows this includes the agent relation
  console.log(sessionWithAgent?.agent?.name); // ✅ agent is included
  console.log(sessionWithAgent?.agentId); // ✅ base fields still available

  // Example 3: Multiple includes
  const sessionWithAll = await getOneSession(
    { id: 'some-id' },
    { 
      agent: true, 
      messages: true, 
      participants: true 
    }
  );
  // TypeScript knows all these relations are included
  console.log(sessionWithAll?.agent?.name);
  console.log(sessionWithAll?.messages?.length);
  console.log(sessionWithAll?.participants?.length);

  // Example 4: Conditional includes with nested selections
  const sessionWithNestedData = await getOneSession(
    { id: 'some-id' },
    {
      agent: {
        select: {
          id: true,
          name: true,
          description: true
        }
      },
      messages: {
        orderBy: { createdAt: 'desc' },
        take: 10
      }
    }
  );
  // TypeScript knows the exact shape of included data
  console.log(sessionWithNestedData?.agent?.name); // ✅ name is selected
  // console.log(sessionWithNestedData?.agent?.povSummary); // ❌ povSummary not selected
}
