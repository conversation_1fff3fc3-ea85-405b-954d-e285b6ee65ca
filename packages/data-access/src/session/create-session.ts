import { z } from 'zod';
import { prisma } from '@repo/database';
import { SessionSchema } from '@repo/entities';
import type { Session } from '@repo/database';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated), createdAt, updatedAt (auto-managed)
// status has a default value, so it's optional
const SessionCreateFieldsSchema = SessionSchema.pick({
  agentId: true,
  status: true,
  type: true,
}).partial({ status: true });

export type SessionCreateFields = z.infer<typeof SessionCreateFieldsSchema>;

export async function createSession(createFields: SessionCreateFields): Promise<Session> {
  // Validate the create fields
  const validatedFields = SessionCreateFieldsSchema.parse(createFields);
  
  return await prisma.session.create({
    data: validatedFields,
  });
}
